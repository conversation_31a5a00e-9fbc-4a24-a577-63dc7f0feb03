/* App.css - Estilos específicos para componentes */

.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Hero Section - Optimizado para conversión */
.hero {
  background: var(--bg-gradient-hero);
  color: white;
  padding: 8rem 0;
  position: relative;
  overflow: hidden;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 30% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(16,185,129,0.1) 0%, transparent 50%),
              radial-gradient(circle at 90% 10%, rgba(245,158,11,0.1) 0%, transparent 50%);
  pointer-events: none;
}

.hero-content {
  max-width: 600px;
  z-index: 10;
  position: relative;
}

.hero h1 {
  color: white;
  font-size: 4rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.05;
  letter-spacing: -0.05em;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-subtitle {
  font-size: 1.25rem;
  font-weight: 400;
  margin-bottom: 2rem;
  opacity: 0.95;
  line-height: 1.5;
  letter-spacing: -0.01em;
}

.hero-cta {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
}

.hero-info {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin-top: 2rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 2rem;
}

.info-icon {
  font-size: 1.25rem;
}

.hero-image {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  width: 45%;
  max-width: 600px;
  z-index: 10;
}

.hero-image img {
  width: 100%;
  height: auto;
  border-radius: 1rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Intro Section */
.intro {
  background: var(--bg-primary);
  position: relative;
}

.intro::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--bg-gradient-hero);
}

.section-content {
  max-width: 800px;
  margin: 0 auto;
}

/* Topics Section */
.topics {
  background: var(--bg-gradient-light);
  position: relative;
}

.topics::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--bg-gradient-hero);
}

.topic-card {
  text-align: center;
  padding: 2.5rem 1.5rem;
}

.topic-icon {
  font-size: 3rem;
  color: var(--primary-color);
  margin-bottom: 1.5rem;
}

/* Instructor Section */
.instructor {
  background: var(--bg-primary);
  position: relative;
  overflow: hidden;
}

.instructor::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--bg-gradient-hero);
}

.instructor::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('data:image/svg+xml;charset=utf8,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"%3E%3Cpath fill="%232563eb" fill-opacity="0.05" d="M0,64L48,80C96,96,192,128,288,128C384,128,480,96,576,90.7C672,85,768,107,864,144C960,181,1056,235,1152,234.7C1248,235,1344,181,1392,154.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"%3E%3C/path%3E%3C/svg%3E');
  background-size: cover;
  background-position: center;
  opacity: 0.2;
}

.instructor-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 3rem;
  align-items: center;
  position: relative;
  z-index: 1;
}

.instructor-image {
  text-align: center;
}

.instructor-image img {
  width: 100%;
  max-width: 300px;
  border-radius: 50%;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  border: 5px solid white;
}

.instructor-title {
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: 1.5rem;
}

/* Benefits Section */
.benefits {
  background: var(--bg-gradient-light);
  position: relative;
}

.benefits::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--bg-gradient-hero);
}

.benefits-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.benefit-item {
  display: flex;
  gap: 1.5rem;
  align-items: flex-start;
}

.benefit-icon {
  font-size: 2.5rem;
  line-height: 1;
}

.benefit-text h3 {
  margin-bottom: 0.5rem;
}

/* Testimonials Section */
.testimonials {
  background-color: white;
}

/* Pricing Section - Optimizada para conversión */
.pricing {
  background: var(--bg-gradient-tech);
  color: white;
  padding: 8rem 0;
  position: relative;
  overflow: hidden;
}

.pricing::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-color) 0%, var(--secondary-color) 100%);
}

.pricing-card {
  background: var(--bg-primary);
  max-width: 600px;
  margin: 0 auto;
  padding: 3rem;
  border-radius: 1.5rem;
  box-shadow: var(--shadow-strong);
  text-align: center;
  border: 3px solid var(--accent-color);
  position: relative;
  overflow: hidden;
  color: #444444;
}

.pricing-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, var(--accent-color) 0%, var(--secondary-color) 100%);
}

.pricing-card h2 {
  color: #444444;
  font-weight: 700;
}

.price {
  margin: 2rem 0;
}

.price-value {
  font-size: 3rem;
  font-weight: 700;
  color: var(--primary-color);
  display: block;
}

.price-period {
  font-size: 1rem;
  color: #666666;
  opacity: 1;
}

.price-features {
  text-align: left;
  max-width: 400px;
  margin: 0 auto 2rem;
  list-style: none;
}

.price-features li {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
  position: relative;
}

.price-note {
  margin-top: 1.5rem;
  font-size: 0.9rem;
  color: #666666;
  opacity: 1;
}

/* FAQ Section */
.faq {
  background-color: white;
}

.faq-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.faq-item h3 {
  margin-bottom: 0.75rem;
  color: var(--primary-color);
}

/* Final CTA Section */
.final-cta {
  background: var(--bg-gradient-tech);
  color: white;
  text-align: center;
  padding: 6rem 0;
  position: relative;
  overflow: hidden;
}

.final-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-color) 0%, var(--secondary-color) 100%);
}

.final-cta h2 {
  color: white;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
}

/* Footer */
.footer {
  background: var(--bg-gradient-tech);
  color: white;
  padding: 3rem 0;
  text-align: center;
  margin-top: auto;
  position: relative;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--accent-color) 0%, var(--secondary-color) 100%);
}

/* Responsive Styles */
@media (max-width: 992px) {
  .hero {
    padding: 4rem 0;
  }
  
  .hero-content {
    max-width: 100%;
    text-align: center;
    margin-bottom: 3rem;
  }
  
  .hero-cta {
    justify-content: center;
  }
  
  .hero-info {
    justify-content: center;
  }
  
  .hero-image {
    position: relative;
    width: 80%;
    max-width: 400px;
    margin: 0 auto;
    top: auto;
    right: auto;
    transform: none;
  }
  
  .instructor-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
  
  .instructor-image {
    margin-bottom: 2rem;
  }
  
  .benefits-content {
    grid-template-columns: 1fr;
  }
  
  .faq-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .hero h1 {
    font-size: 2.5rem;
  }
  
  .hero-cta {
    flex-direction: column;
  }
  
  .benefit-item {
    flex-direction: column;
    text-align: center;
  }
  
  .pricing-card {
    padding: 2rem 1.5rem;
  }
  
  .cta-buttons {
    flex-direction: column;
  }
}

@media (max-width: 576px) {
  .hero h1 {
    font-size: 2rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
  
  .info-item {
    width: 100%;
    justify-content: center;
  }
}

/* WhatsApp Button */
.whatsapp-btn {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  background-color: #25D366;
  color: white;
  border: none;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
  transition: all 0.3s ease;
  z-index: 1000;
}

.whatsapp-btn:hover {
  background-color: #128C7E;
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(37, 211, 102, 0.6);
}

/* Checkout Modal */
.checkout-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.checkout-modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
}

.checkout-modal-content {
  position: relative;
  z-index: 2001;
  max-width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  border-radius: 1rem;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Secciones con partículas */
.countdown-section {
  position: relative;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  overflow: hidden;
  padding: 4rem 0;
}

.countdown-section .container {
  position: relative;
  z-index: 10;
}

.section.topics {
  position: relative;
  overflow: hidden;
}

.section.pricing {
  position: relative;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  overflow: hidden;
}

.section.topics .container,
.section.pricing .container {
  position: relative;
  z-index: 10;
}

/* Responsive para modal */
@media (max-width: 768px) {
  .checkout-modal {
    padding: 0.5rem;
  }

  .checkout-modal-content {
    max-height: 95vh;
  }

  .countdown-section {
    padding: 2rem 0;
  }
}
