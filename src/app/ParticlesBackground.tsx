import React, { useEffect, useRef } from 'react';

interface ParticlesBackgroundProps {
  id?: string;
}

interface Particle {
  x: number;
  y: number;
  vx: number;
  vy: number;
  size: number;
  opacity: number;
  color: string;
}

const ParticlesBackground: React.FC<ParticlesBackgroundProps> = ({ id = "particles-canvas" }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const particlesRef = useRef<Particle[]>([]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Configuración responsiva con mayor densidad
    const isMobile = window.innerWidth < 768;
    const isTablet = window.innerWidth < 1024;

    let particleCount: number;
    let connectionDistance: number;

    if (isMobile) {
      particleCount = 35; // Aumentado de 20 a 35
      connectionDistance = 100; // Aumentado de 80 a 100
    } else if (isTablet) {
      particleCount = 55; // Nuevo nivel para tablets
      connectionDistance = 130;
    } else {
      particleCount = 70; // Aumentado de 40 a 70
      connectionDistance = 150; // Aumentado de 120 a 150
    }

    // Colores del esquema de marca
    const colors = ['#ffffff', '#3b82f6', '#10b981'];

    // Función para redimensionar canvas
    const resizeCanvas = () => {
      canvas.width = canvas.offsetWidth;
      canvas.height = canvas.offsetHeight;
    };

    // Crear partículas
    const createParticles = () => {
      particlesRef.current = [];
      for (let i = 0; i < particleCount; i++) {
        particlesRef.current.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          vx: (Math.random() - 0.5) * 0.8, // Velocidad ligeramente aumentada
          vy: (Math.random() - 0.5) * 0.8,
          size: Math.random() * 2.5 + 0.8, // Tamaños más variados (0.8-3.3)
          opacity: Math.random() * 0.4 + 0.4, // Opacidad más consistente (0.4-0.8)
          color: colors[Math.floor(Math.random() * colors.length)]
        });
      }
    };

    // Animar partículas
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Actualizar y dibujar partículas
      particlesRef.current.forEach((particle, i) => {
        // Actualizar posición
        particle.x += particle.vx;
        particle.y += particle.vy;

        // Rebotar en los bordes
        if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
        if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;

        // Mantener dentro del canvas
        particle.x = Math.max(0, Math.min(canvas.width, particle.x));
        particle.y = Math.max(0, Math.min(canvas.height, particle.y));

        // Dibujar partícula
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fillStyle = particle.color;
        ctx.globalAlpha = particle.opacity;
        ctx.fill();

        // Dibujar conexiones
        particlesRef.current.slice(i + 1).forEach(otherParticle => {
          const dx = particle.x - otherParticle.x;
          const dy = particle.y - otherParticle.y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < connectionDistance) {
            const opacity = (1 - distance / connectionDistance) * 0.4; // Aumentado de 0.3 a 0.4
            ctx.beginPath();
            ctx.moveTo(particle.x, particle.y);
            ctx.lineTo(otherParticle.x, otherParticle.y);
            ctx.strokeStyle = '#ffffff';
            ctx.globalAlpha = opacity;
            ctx.lineWidth = 1.2; // Líneas ligeramente más gruesas
            ctx.stroke();
          }
        });
      });

      animationRef.current = requestAnimationFrame(animate);
    };

    // Inicializar
    resizeCanvas();
    createParticles();
    animate();

    // Event listeners
    const handleResize = () => {
      resizeCanvas();
      createParticles();
    };

    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      id={id}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: 1,
        pointerEvents: 'none',
      }}
    />
  );
};

export default ParticlesBackground;
