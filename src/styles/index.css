@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700;800&family=Open+Sans:wght@400;500;600&display=swap');

:root {
  /* Colores basados en evidencia científica de neuromarketing */
  --primary-color: #2563eb; /* Azul tecnológico - Confianza y profesionalismo (87% empresas tech) */
  --primary-light: #3b82f6; /* Azul más claro para gradientes */
  --primary-dark: #1d4ed8; /* Azul más oscuro para hover */

  --secondary-color: #10b981; /* Verde - Crecimiento y éxito */
  --accent-color: #f59e0b; /* Naranja - Urgencia y acción (aumenta conversiones 21%) */
  --accent-hover: #d97706; /* Naranja oscuro para hover */

  --dark-color: #1e293b; /* Azul oscuro - Autoridad y tecnología */
  --dark-secondary: #334155; /* Gris azulado para texto */

  /* Fondos híbridos para máxima efectividad */
  --bg-primary: #ffffff; /* Blanco puro para contenido */
  --bg-secondary: #f8fafc; /* Blanco hueso para secciones alternas */
  --bg-gradient-light: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
  --bg-gradient-tech: linear-gradient(135deg, #1e293b 0%, #2563eb 100%);
  --bg-gradient-hero: linear-gradient(135deg, #2563eb 0%, #3b82f6 50%, #60a5fa 100%);

  --text-primary: #1e293b; /* Texto principal - máximo contraste */
  --text-secondary: #475569; /* Texto secundario */
  --text-light: #64748b; /* Texto suave */

  --success-color: #10b981; /* Verde - Confirmación */
  --error-color: #ef4444; /* Rojo - Alertas */

  /* Sombras mejoradas para profundidad */
  --shadow-soft: 0 4px 6px rgba(37, 99, 235, 0.1);
  --shadow-medium: 0 10px 15px rgba(37, 99, 235, 0.15);
  --shadow-strong: 0 20px 25px rgba(37, 99, 235, 0.2);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Open Sans', sans-serif;
  color: var(--text-primary);
  line-height: 1.6;
  background: var(--bg-gradient-light);
  min-height: 100vh;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

h1 {
  font-size: 3rem;
  font-weight: 800;
}

h2 {
  font-size: 2.5rem;
}

h3 {
  font-size: 2rem;
}

p {
  margin-bottom: 1.5rem;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  font-size: 1rem;
}

.btn-primary {
  background: var(--bg-gradient-hero);
  color: white;
  box-shadow: var(--shadow-medium);
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
}

.btn-primary:hover {
  background: var(--primary-dark);
  transform: translateY(-3px);
  box-shadow: var(--shadow-strong);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
  box-shadow: 0 4px 6px rgba(16, 185, 129, 0.25);
}

.btn-secondary:hover {
  background-color: #059669;
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(16, 185, 129, 0.3);
}

.btn-accent {
  background: linear-gradient(135deg, var(--accent-color) 0%, #fb923c 100%);
  color: white;
  box-shadow: var(--shadow-medium);
  border: 2px solid var(--accent-color);
  font-weight: 600;
  position: relative;
  overflow: hidden;
}

.btn-accent:hover {
  background: var(--accent-hover);
  transform: translateY(-3px);
  box-shadow: var(--shadow-strong);
  border-color: var(--accent-hover);
}

.btn-accent::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn-accent:hover::before {
  left: 100%;
}

.btn-large {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

.text-center {
  text-align: center;
}

.section {
  padding: 5rem 0;
}

.section-title {
  text-align: center;
  margin-bottom: 3rem;
}

.grid {
  display: grid;
  gap: 2rem;
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

.card {
  background: var(--bg-primary);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: var(--shadow-soft);
  transition: all 0.3s ease;
  border: 1px solid rgba(37, 99, 235, 0.1);
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--bg-gradient-hero);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-medium);
  border-color: rgba(37, 99, 235, 0.2);
}

.card:hover::before {
  transform: scaleX(1);
}

.feature-icon {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.whatsapp-btn {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  background-color: #25D366;
  color: white;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 10px rgba(37, 211, 102, 0.4);
  z-index: 100;
  transition: all 0.3s ease;
}

.whatsapp-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 14px rgba(37, 211, 102, 0.5);
}

.highlight {
  color: var(--primary-color);
  font-weight: 600;
}

.testimonial {
  font-style: italic;
  position: relative;
  padding: 1.5rem;
  background-color: rgba(37, 99, 235, 0.05);
  border-left: 4px solid var(--primary-color);
  margin-bottom: 2rem;
}

.testimonial-author {
  font-weight: 600;
  font-style: normal;
  margin-top: 1rem;
  display: block;
}

/* Responsive styles */
@media (max-width: 992px) {
  .grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  h1 {
    font-size: 2.5rem;
  }
  
  h2 {
    font-size: 2rem;
  }
  
  h3 {
    font-size: 1.75rem;
  }
  
  .grid-2, .grid-3, .grid-4 {
    grid-template-columns: 1fr;
  }
  
  .section {
    padding: 3rem 0;
  }
}

@media (max-width: 576px) {
  h1 {
    font-size: 2rem;
  }
  
  h2 {
    font-size: 1.75rem;
  }
  
  h3 {
    font-size: 1.5rem;
  }
  
  .btn-large {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }
}
