.success-page {
  min-height: 100vh;
  background: var(--bg-gradient-light);
  padding: 2rem 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.success-container {
  max-width: 800px;
  background: var(--bg-primary);
  border-radius: 1.5rem;
  padding: 3rem;
  box-shadow: var(--shadow-strong);
  border: 2px solid var(--secondary-color);
  text-align: center;
}

.success-icon {
  font-size: 4rem;
  color: var(--secondary-color);
  margin-bottom: 1.5rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.success-container h1 {
  color: var(--text-primary);
  font-size: 2.5rem;
  margin-bottom: 1rem;
  font-weight: 700;
}

.success-subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
  line-height: 1.5;
}

.success-details {
  background: rgba(37, 99, 235, 0.05);
  border-radius: 1rem;
  padding: 1.5rem;
  margin: 2rem 0;
  border: 1px solid rgba(37, 99, 235, 0.1);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(37, 99, 235, 0.1);
  font-size: 1.1rem;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item strong {
  color: var(--text-primary);
  font-weight: 600;
}

.next-steps {
  text-align: left;
  margin: 2rem 0;
  background: rgba(16, 185, 129, 0.05);
  padding: 1.5rem;
  border-radius: 1rem;
  border: 1px solid rgba(16, 185, 129, 0.1);
}

.next-steps h2 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  text-align: center;
  font-size: 1.5rem;
}

.next-steps ol {
  padding-left: 1.5rem;
}

.next-steps li {
  margin-bottom: 1rem;
  line-height: 1.6;
  color: var(--text-secondary);
}

.next-steps li strong {
  color: var(--text-primary);
}

.contact-options {
  margin: 2rem 0;
  padding: 1.5rem;
  background: rgba(245, 158, 11, 0.05);
  border-radius: 1rem;
  border: 1px solid rgba(245, 158, 11, 0.1);
}

.contact-options h3 {
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.contact-options p {
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.contact-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.contact-buttons .btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-family: 'Inter', sans-serif;
}

.btn-whatsapp {
  background: #25D366;
  color: white;
}

.btn-whatsapp:hover {
  background: #128C7E;
  transform: translateY(-2px);
}

.btn-email {
  background: var(--primary-color);
  color: white;
}

.btn-email:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
}

.course-info {
  text-align: left;
  margin: 2rem 0;
  background: rgba(37, 99, 235, 0.05);
  padding: 1.5rem;
  border-radius: 1rem;
  border: 1px solid rgba(37, 99, 235, 0.1);
}

.course-info h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  text-align: center;
  font-size: 1.5rem;
}

.course-info ul {
  list-style: none;
  padding: 0;
}

.course-info li {
  margin-bottom: 0.75rem;
  color: var(--text-secondary);
  font-size: 1.1rem;
  line-height: 1.5;
}

.social-share {
  margin: 2rem 0;
  padding: 1.5rem;
  background: rgba(16, 185, 129, 0.05);
  border-radius: 1rem;
  border: 1px solid rgba(16, 185, 129, 0.1);
}

.social-share h3 {
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.social-share p {
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.share-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.share-buttons .btn {
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Inter', sans-serif;
}

.btn-twitter {
  background: #1DA1F2;
  color: white;
}

.btn-twitter:hover {
  background: #0d8bd9;
  transform: translateY(-2px);
}

.btn-facebook {
  background: #4267B2;
  color: white;
}

.btn-facebook:hover {
  background: #365899;
  transform: translateY(-2px);
}

.btn-linkedin {
  background: #0077B5;
  color: white;
}

.btn-linkedin:hover {
  background: #005885;
  transform: translateY(-2px);
}

.back-home {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 2px solid rgba(37, 99, 235, 0.1);
}

.back-home .btn-primary {
  background: var(--bg-gradient-hero);
  color: white;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  font-weight: 600;
  border: none;
  cursor: pointer;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  font-family: 'Inter', sans-serif;
}

.back-home .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

/* Responsive */
@media (max-width: 768px) {
  .success-page {
    padding: 1rem;
  }
  
  .success-container {
    padding: 2rem 1.5rem;
  }
  
  .success-container h1 {
    font-size: 2rem;
  }
  
  .success-subtitle {
    font-size: 1.1rem;
  }
  
  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .contact-buttons,
  .share-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .contact-buttons .btn,
  .share-buttons .btn {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }
}
