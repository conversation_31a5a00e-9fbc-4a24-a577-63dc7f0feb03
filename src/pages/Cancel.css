.cancel-page {
  min-height: 100vh;
  background: var(--bg-gradient-light);
  padding: 2rem 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-container {
  max-width: 800px;
  background: var(--bg-primary);
  border-radius: 1.5rem;
  padding: 3rem;
  box-shadow: var(--shadow-strong);
  border: 2px solid #ef4444;
  text-align: center;
}

.cancel-icon {
  font-size: 4rem;
  color: #ef4444;
  margin-bottom: 1.5rem;
  animation: shake 1s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.cancel-container h1 {
  color: var(--text-primary);
  font-size: 2.5rem;
  margin-bottom: 1rem;
  font-weight: 700;
}

.cancel-subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
  line-height: 1.5;
}

.cancel-message {
  background: rgba(239, 68, 68, 0.05);
  border-radius: 1rem;
  padding: 1.5rem;
  margin: 2rem 0;
  border: 1px solid rgba(239, 68, 68, 0.1);
}

.cancel-message p {
  color: var(--text-secondary);
  font-size: 1.1rem;
  line-height: 1.6;
  margin: 0;
}

.possible-reasons {
  text-align: left;
  margin: 2rem 0;
  background: rgba(245, 158, 11, 0.05);
  padding: 1.5rem;
  border-radius: 1rem;
  border: 1px solid rgba(245, 158, 11, 0.1);
}

.possible-reasons h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  text-align: center;
  font-size: 1.5rem;
}

.possible-reasons ul {
  padding-left: 1.5rem;
  margin: 0;
}

.possible-reasons li {
  margin-bottom: 0.75rem;
  color: var(--text-secondary);
  line-height: 1.5;
}

.solutions {
  margin: 2rem 0;
}

.solutions h3 {
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
}

.solution-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.solution-card {
  background: rgba(37, 99, 235, 0.05);
  border-radius: 1rem;
  padding: 1.5rem;
  border: 1px solid rgba(37, 99, 235, 0.1);
  text-align: center;
}

.solution-card h4 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.solution-card p {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.solution-card .btn {
  width: 100%;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Inter', sans-serif;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-primary {
  background: var(--bg-gradient-hero);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.btn-whatsapp {
  background: #25D366;
  color: white;
}

.btn-whatsapp:hover {
  background: #128C7E;
  transform: translateY(-2px);
}

.btn-oxxo {
  background: linear-gradient(135deg, #f59e0b 0%, #fb923c 100%);
  color: white;
}

.btn-oxxo:hover {
  background: #d97706;
  transform: translateY(-2px);
}

.course-reminder {
  margin: 2rem 0;
  background: rgba(16, 185, 129, 0.05);
  padding: 1.5rem;
  border-radius: 1rem;
  border: 1px solid rgba(16, 185, 129, 0.1);
}

.course-reminder h3 {
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.benefit {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: rgba(16, 185, 129, 0.1);
  border-radius: 0.5rem;
}

.benefit-icon {
  font-size: 1.5rem;
}

.benefit span:last-child {
  color: var(--text-primary);
  font-weight: 500;
}

.urgency-message {
  margin: 2rem 0;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  padding: 1.5rem;
  border-radius: 1rem;
  position: relative;
  overflow: hidden;
}

.urgency-message::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  animation: shine 2s infinite;
}

@keyframes shine {
  0% { left: -100%; }
  100% { left: 100%; }
}

.urgency-content h4 {
  margin-bottom: 0.5rem;
  font-size: 1.25rem;
}

.urgency-content p {
  margin: 0;
  line-height: 1.5;
}

.alternative-contact {
  margin: 2rem 0;
  text-align: left;
  background: rgba(37, 99, 235, 0.05);
  padding: 1.5rem;
  border-radius: 1rem;
  border: 1px solid rgba(37, 99, 235, 0.1);
}

.alternative-contact h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  text-align: center;
  font-size: 1.5rem;
}

.contact-methods {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.contact-method {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background: rgba(37, 99, 235, 0.1);
  border-radius: 0.5rem;
}

.contact-method strong {
  color: var(--text-primary);
  min-width: 100px;
}

.contact-method a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

.contact-method a:hover {
  text-decoration: underline;
}

.navigation-buttons {
  display: flex;
  gap: 1rem;
  margin: 2rem 0;
  justify-content: center;
}

.navigation-buttons .btn {
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Inter', sans-serif;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-secondary {
  background: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background: #4b5563;
  transform: translateY(-2px);
}

.btn-accent {
  background: linear-gradient(135deg, var(--accent-color) 0%, #fb923c 100%);
  color: white;
}

.btn-accent:hover {
  background: var(--accent-hover);
  transform: translateY(-2px);
}

.security-note {
  margin-top: 2rem;
  padding: 1rem;
  background: rgba(16, 185, 129, 0.05);
  border-radius: 0.75rem;
  border: 1px solid rgba(16, 185, 129, 0.1);
}

.security-note p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.95rem;
  line-height: 1.5;
}

/* Responsive */
@media (max-width: 768px) {
  .cancel-page {
    padding: 1rem;
  }
  
  .cancel-container {
    padding: 2rem 1.5rem;
  }
  
  .cancel-container h1 {
    font-size: 2rem;
  }
  
  .cancel-subtitle {
    font-size: 1.1rem;
  }
  
  .solution-options {
    grid-template-columns: 1fr;
  }
  
  .benefits-grid {
    grid-template-columns: 1fr;
  }
  
  .navigation-buttons {
    flex-direction: column;
  }
  
  .contact-method {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
