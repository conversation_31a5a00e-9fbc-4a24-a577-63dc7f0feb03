import React, { useEffect } from 'react';
import { FaCheckCircle, FaWhatsapp, FaEnvelope } from 'react-icons/fa';
import './Success.css';

const Success: React.FC = () => {
  useEffect(() => {
    // Scroll to top cuando se carga la página
    window.scrollTo(0, 0);
    
    // Opcional: Enviar evento de conversión a Google Analytics
    if (typeof gtag !== 'undefined') {
      gtag('event', 'purchase', {
        transaction_id: new URLSearchParams(window.location.search).get('payment_intent'),
        value: 500,
        currency: 'MXN',
        items: [{
          item_id: 'curso-ia-2024',
          item_name: 'Curso de Inteligencia Artificial 2024',
          category: 'Educación',
          quantity: 1,
          price: 500
        }]
      });
    }
  }, []);

  const handleWhatsApp = () => {
    const message = encodeURIComponent(
      '¡Hola! Acabo de completar mi pago para el Curso de Inteligencia Artificial. ¿Podrías confirmar mi inscripción y enviarme los detalles de acceso?'
    );
    window.open(`https://wa.me/529996406775?text=${message}`, '_blank');
  };

  const handleEmail = () => {
    const subject = encodeURIComponent('Confirmación de Pago - Curso IA 2024');
    const body = encodeURIComponent(
      'Hola Will,\n\nAcabo de completar mi pago para el Curso de Inteligencia Artificial 2024.\n\n' +
      'Por favor confirma mi inscripción y envíame los detalles de acceso.\n\n' +
      'Gracias!'
    );
    window.location.href = `mailto:<EMAIL>?subject=${subject}&body=${body}`;
  };

  return (
    <div className="success-page">
      <div className="success-container">
        <div className="success-icon">
          <FaCheckCircle />
        </div>
        
        <h1>¡Pago Exitoso!</h1>
        <p className="success-subtitle">
          Tu inscripción al Curso de Inteligencia Artificial ha sido confirmada
        </p>

        <div className="success-details">
          <div className="detail-item">
            <strong>Curso:</strong> Inteligencia Artificial 2024
          </div>
          <div className="detail-item">
            <strong>Instructor:</strong> Will de la Vega
          </div>
          <div className="detail-item">
            <strong>Duración:</strong> 7 días intensivos
          </div>
          <div className="detail-item">
            <strong>Modalidad:</strong> Online en vivo
          </div>
        </div>

        <div className="next-steps">
          <h2>📋 Próximos pasos:</h2>
          <ol>
            <li>
              <strong>Confirmación por email:</strong> Recibirás un email de confirmación 
              con todos los detalles del curso en los próximos 15 minutos.
            </li>
            <li>
              <strong>Acceso al curso:</strong> Te enviaremos el enlace de acceso y 
              materiales 24 horas antes del inicio.
            </li>
            <li>
              <strong>Grupo de WhatsApp:</strong> Serás agregado al grupo exclusivo 
              de participantes para soporte y networking.
            </li>
            <li>
              <strong>Preparación:</strong> Revisa los requisitos técnicos que 
              recibirás por email.
            </li>
          </ol>
        </div>

        <div className="contact-options">
          <h3>¿Necesitas ayuda?</h3>
          <p>Contáctanos si tienes alguna pregunta:</p>
          
          <div className="contact-buttons">
            <button onClick={handleWhatsApp} className="btn btn-whatsapp">
              <FaWhatsapp />
              WhatsApp
            </button>
            
            <button onClick={handleEmail} className="btn btn-email">
              <FaEnvelope />
              Email
            </button>
          </div>
        </div>

        <div className="course-info">
          <h3>🚀 ¿Qué incluye tu curso?</h3>
          <ul>
            <li>✅ 7 sesiones en vivo de 1 hora cada una</li>
            <li>✅ Acceso a grabaciones por 6 meses</li>
            <li>✅ Material complementario descargable</li>
            <li>✅ Proyectos prácticos con feedback</li>
            <li>✅ Certificado de participación</li>
            <li>✅ Acceso al grupo privado de alumnos</li>
            <li>✅ Soporte directo con Will de la Vega</li>
          </ul>
        </div>

        <div className="social-share">
          <h3>📢 ¡Comparte tu logro!</h3>
          <p>Cuéntales a tus amigos que te estás preparando para el futuro:</p>
          
          <div className="share-buttons">
            <button 
              onClick={() => {
                const text = encodeURIComponent('¡Me inscribí al Curso de Inteligencia Artificial con Will de la Vega! 🚀 #IA #Tecnología #Aprendizaje');
                window.open(`https://twitter.com/intent/tweet?text=${text}&url=https://cursos.willdelavega.com`, '_blank');
              }}
              className="btn btn-twitter"
            >
              🐦 Twitter
            </button>
            
            <button 
              onClick={() => {
                const url = encodeURIComponent('https://cursos.willdelavega.com');
                const text = encodeURIComponent('¡Me inscribí al Curso de Inteligencia Artificial con Will de la Vega! Aprenderé ChatGPT, Claude, Midjourney y más. ¡Súper emocionado!');
                window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}&quote=${text}`, '_blank');
              }}
              className="btn btn-facebook"
            >
              📘 Facebook
            </button>
            
            <button 
              onClick={() => {
                const url = encodeURIComponent('https://cursos.willdelavega.com');
                const text = encodeURIComponent('¡Me inscribí al Curso de Inteligencia Artificial con Will de la Vega! Aprenderé las herramientas más avanzadas de IA para transformar mi carrera profesional.');
                window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}&summary=${text}`, '_blank');
              }}
              className="btn btn-linkedin"
            >
              💼 LinkedIn
            </button>
          </div>
        </div>

        <div className="back-home">
          <button 
            onClick={() => window.location.href = '/'}
            className="btn btn-primary"
          >
            🏠 Volver al inicio
          </button>
        </div>
      </div>
    </div>
  );
};

export default Success;
