import React from 'react';
import { FaTimesCircle, FaWhatsapp, FaArrowLeft } from 'react-icons/fa';
import './Cancel.css';

const Cancel: React.FC = () => {
  const handleWhatsApp = () => {
    const message = encodeURIComponent(
      'Hola, tuve problemas con el pago del Curso de Inteligencia Artificial. ¿Podrías ayudarme?'
    );
    window.open(`https://wa.me/529996406775?text=${message}`, '_blank');
  };

  const handleRetry = () => {
    window.location.href = '/#pricing';
  };

  const handleGoHome = () => {
    window.location.href = '/';
  };

  return (
    <div className="cancel-page">
      <div className="cancel-container">
        <div className="cancel-icon">
          <FaTimesCircle />
        </div>
        
        <h1>Pago Cancelado</h1>
        <p className="cancel-subtitle">
          No se procesó tu pago para el Curso de Inteligencia Artificial
        </p>

        <div className="cancel-message">
          <p>
            No te preocupes, esto puede suceder por diferentes razones. 
            Tu lugar en el curso sigue disponible y puedes intentar nuevamente.
          </p>
        </div>

        <div className="possible-reasons">
          <h3>🤔 Posibles razones:</h3>
          <ul>
            <li>Cancelaste el proceso de pago</li>
            <li>Problemas temporales con tu método de pago</li>
            <li>Conexión a internet interrumpida</li>
            <li>Límites en tu tarjeta o cuenta</li>
          </ul>
        </div>

        <div className="solutions">
          <h3>💡 ¿Qué puedes hacer?</h3>
          <div className="solution-options">
            <div className="solution-card">
              <h4>🔄 Intentar nuevamente</h4>
              <p>
                Regresa a la página de pago y prueba con el mismo método 
                o uno diferente.
              </p>
              <button onClick={handleRetry} className="btn btn-primary">
                Intentar de nuevo
              </button>
            </div>

            <div className="solution-card">
              <h4>💬 Contactar soporte</h4>
              <p>
                Si sigues teniendo problemas, contáctanos por WhatsApp 
                para ayudarte personalmente.
              </p>
              <button onClick={handleWhatsApp} className="btn btn-whatsapp">
                <FaWhatsapp />
                WhatsApp
              </button>
            </div>

            <div className="solution-card">
              <h4>🏪 Pago en OXXO</h4>
              <p>
                Prueba con el método de pago en efectivo en OXXO, 
                es 100% seguro y confiable.
              </p>
              <button onClick={handleRetry} className="btn btn-oxxo">
                Pagar en OXXO
              </button>
            </div>
          </div>
        </div>

        <div className="course-reminder">
          <h3>🚀 Recuerda lo que incluye el curso:</h3>
          <div className="benefits-grid">
            <div className="benefit">
              <span className="benefit-icon">🧠</span>
              <span>ChatGPT, Claude, Midjourney</span>
            </div>
            <div className="benefit">
              <span className="benefit-icon">⏱️</span>
              <span>7 días intensivos</span>
            </div>
            <div className="benefit">
              <span className="benefit-icon">👨‍💻</span>
              <span>Will de la Vega (35 años exp.)</span>
            </div>
            <div className="benefit">
              <span className="benefit-icon">💰</span>
              <span>Precio especial $500 MXN</span>
            </div>
            <div className="benefit">
              <span className="benefit-icon">📜</span>
              <span>Certificado incluido</span>
            </div>
            <div className="benefit">
              <span className="benefit-icon">🎥</span>
              <span>Grabaciones por 6 meses</span>
            </div>
          </div>
        </div>

        <div className="urgency-message">
          <div className="urgency-content">
            <h4>⏰ ¡Plazas limitadas!</h4>
            <p>
              Solo quedan pocas plazas disponibles para garantizar 
              atención personalizada. No pierdas tu oportunidad.
            </p>
          </div>
        </div>

        <div className="alternative-contact">
          <h3>📞 Otras formas de contacto:</h3>
          <div className="contact-methods">
            <div className="contact-method">
              <strong>📧 Email:</strong>
              <a href="mailto:<EMAIL>">
                <EMAIL>
              </a>
            </div>
            <div className="contact-method">
              <strong>📱 WhatsApp:</strong>
              <a href="https://wa.me/529996406775" target="_blank" rel="noopener noreferrer">
                +52 999 640 6775
              </a>
            </div>
          </div>
        </div>

        <div className="navigation-buttons">
          <button onClick={handleGoHome} className="btn btn-secondary">
            <FaArrowLeft />
            Volver al inicio
          </button>
          
          <button onClick={handleRetry} className="btn btn-accent">
            🔄 Intentar pago nuevamente
          </button>
        </div>

        <div className="security-note">
          <p>
            🔒 <strong>Nota de seguridad:</strong> Todos los pagos son procesados 
            de forma segura por Stripe, líder mundial en procesamiento de pagos. 
            Tus datos están completamente protegidos.
          </p>
        </div>
      </div>
    </div>
  );
};

export default Cancel;
