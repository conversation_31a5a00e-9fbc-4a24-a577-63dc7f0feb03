// Utilidades para manejo de fechas del curso

/**
 * <PERSON>cula la fecha del próximo lunes a las 7:00 PM (México)
 * @returns Date objeto con la fecha del próximo lunes a las 7:00 PM
 */
export const getNextMondayAt7PM = (): Date => {
  const now = new Date();
  const nextMonday = new Date();
  
  // Obtener el día de la semana (0 = domingo, 1 = lunes, etc.)
  const currentDay = now.getDay();
  
  // Calcular días hasta el próximo lunes
  let daysUntilMonday = 1 - currentDay; // 1 = lunes
  
  // Si ya es lunes, verificar si ya pasaron las 7:00 PM
  if (currentDay === 1) {
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    
    // Si ya pasaron las 7:00 PM del lunes actual, ir al siguiente lunes
    if (currentHour > 19 || (currentHour === 19 && currentMinute > 0)) {
      daysUntilMonday = 7;
    } else {
      daysUntilMonday = 0; // Es lunes y aún no son las 7:00 PM
    }
  }
  
  // Si es domingo (día 0), el próximo lunes es mañana
  if (currentDay === 0) {
    daysUntilMonday = 1;
  }
  
  // Si daysUntilMonday es negativo, agregar 7 días para ir al siguiente lunes
  if (daysUntilMonday < 0) {
    daysUntilMonday += 7;
  }
  
  // Configurar la fecha del próximo lunes
  nextMonday.setDate(now.getDate() + daysUntilMonday);
  nextMonday.setHours(19, 0, 0, 0); // 7:00 PM, 0 minutos, 0 segundos, 0 milisegundos
  
  return nextMonday;
};

/**
 * Formatea una fecha para mostrar en formato legible
 * @param date - Fecha a formatear
 * @returns String con la fecha formateada
 */
export const formatCourseDate = (date: Date): string => {
  const options: Intl.DateTimeFormatOptions = {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    timeZone: 'America/Mexico_City'
  };
  
  return date.toLocaleDateString('es-MX', options);
};

/**
 * Obtiene información detallada del curso
 * @returns Objeto con información del curso
 */
export const getCourseInfo = () => {
  const startDate = getNextMondayAt7PM();
  
  return {
    startDate,
    formattedDate: formatCourseDate(startDate),
    duration: '7 días intensivos',
    schedule: 'Lunes a Viernes, 7:00 PM - 9:00 PM (México)',
    timezone: 'Hora del Centro de México (CST/CDT)',
    format: 'En vivo por Zoom',
    instructor: 'Will de la Vega'
  };
};

/**
 * Verifica si el curso ya comenzó
 * @returns boolean indicando si el curso ya comenzó
 */
export const hasCourseStarted = (): boolean => {
  const now = new Date();
  const courseStart = getNextMondayAt7PM();
  
  return now >= courseStart;
};

/**
 * Calcula el tiempo restante hasta el inicio del curso
 * @returns Objeto con días, horas, minutos y segundos restantes
 */
export const getTimeUntilCourse = () => {
  const now = new Date();
  const courseStart = getNextMondayAt7PM();
  const difference = courseStart.getTime() - now.getTime();
  
  if (difference <= 0) {
    return { days: 0, hours: 0, minutes: 0, seconds: 0 };
  }
  
  return {
    days: Math.floor(difference / (1000 * 60 * 60 * 24)),
    hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
    minutes: Math.floor((difference / 1000 / 60) % 60),
    seconds: Math.floor((difference / 1000) % 60)
  };
};
