/* App.css - Estilos específicos para componentes */

.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Hero Section */
.hero {
  background: linear-gradient(135deg, var(--dark-color) 0%, var(--primary-color) 100%);
  color: white;
  padding: 6rem 0;
  position: relative;
  overflow: hidden;
}

.hero::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('data:image/svg+xml;charset=utf8,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"%3E%3Cpath fill="%23ffffff" fill-opacity="0.05" d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,218.7C672,235,768,245,864,234.7C960,224,1056,192,1152,176C1248,160,1344,160,1392,160L1440,160L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"%3E%3C/path%3E%3C/svg%3E');
  background-size: cover;
  background-position: center;
  opacity: 0.1;
}

.hero-content {
  max-width: 600px;
  z-index: 1;
  position: relative;
}

.hero h1 {
  color: white;
  font-size: 3.5rem;
  margin-bottom: 1.5rem;
  line-height: 1.1;
}

.hero-subtitle {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.hero-cta {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
}

.hero-info {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin-top: 2rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 2rem;
}

.info-icon {
  font-size: 1.25rem;
}

.hero-image {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 45%;
  max-width: 600px;
  z-index: 1;
}

.hero-image img {
  width: 100%;
  height: auto;
  border-radius: 1rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Intro Section */
.intro {
  background-color: white;
}

.section-content {
  max-width: 800px;
  margin: 0 auto;
}

/* Topics Section */
.topics {
  background-color: var(--light-color);
}

.topic-card {
  text-align: center;
  padding: 2.5rem 1.5rem;
}

.topic-icon {
  font-size: 3rem;
  color: var(--primary-color);
  margin-bottom: 1.5rem;
}

/* Instructor Section */
.instructor {
  background-color: white;
  position: relative;
  overflow: hidden;
}

.instructor::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('data:image/svg+xml;charset=utf8,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"%3E%3Cpath fill="%232563eb" fill-opacity="0.05" d="M0,64L48,80C96,96,192,128,288,128C384,128,480,96,576,90.7C672,85,768,107,864,144C960,181,1056,235,1152,234.7C1248,235,1344,181,1392,154.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"%3E%3C/path%3E%3C/svg%3E');
  background-size: cover;
  background-position: center;
  opacity: 0.2;
}

.instructor-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 3rem;
  align-items: center;
  position: relative;
  z-index: 1;
}

.instructor-image {
  text-align: center;
}

.instructor-image img {
  width: 100%;
  max-width: 300px;
  border-radius: 50%;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  border: 5px solid white;
}

.instructor-title {
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: 1.5rem;
}

/* Benefits Section */
.benefits {
  background-color: var(--light-color);
}

.benefits-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.benefit-item {
  display: flex;
  gap: 1.5rem;
  align-items: flex-start;
}

.benefit-icon {
  font-size: 2.5rem;
  line-height: 1;
}

.benefit-text h3 {
  margin-bottom: 0.5rem;
}

/* Testimonials Section */
.testimonials {
  background-color: white;
}

/* Pricing Section */
.pricing {
  background-color: var(--light-color);
  padding: 6rem 0;
}

.pricing-card {
  background-color: white;
  max-width: 600px;
  margin: 0 auto;
  padding: 3rem;
  border-radius: 1rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.price {
  margin: 2rem 0;
}

.price-value {
  font-size: 3rem;
  font-weight: 700;
  color: var(--primary-color);
  display: block;
}

.price-period {
  font-size: 1rem;
  opacity: 0.7;
}

.price-features {
  text-align: left;
  max-width: 400px;
  margin: 0 auto 2rem;
  list-style: none;
}

.price-features li {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
  position: relative;
}

.price-note {
  margin-top: 1.5rem;
  font-size: 0.9rem;
  opacity: 0.7;
}

/* FAQ Section */
.faq {
  background-color: white;
}

.faq-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.faq-item h3 {
  margin-bottom: 0.75rem;
  color: var(--primary-color);
}

/* Final CTA Section */
.final-cta {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-color) 100%);
  color: white;
  text-align: center;
  padding: 5rem 0;
}

.final-cta h2 {
  color: white;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
}

/* Footer */
.footer {
  background-color: var(--dark-color);
  color: white;
  padding: 2rem 0;
  text-align: center;
  margin-top: auto;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .hero {
    padding: 4rem 0;
  }
  
  .hero-content {
    max-width: 100%;
    text-align: center;
    margin-bottom: 3rem;
  }
  
  .hero-cta {
    justify-content: center;
  }
  
  .hero-info {
    justify-content: center;
  }
  
  .hero-image {
    position: relative;
    width: 80%;
    max-width: 400px;
    margin: 0 auto;
    top: auto;
    right: auto;
    transform: none;
  }
  
  .instructor-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
  
  .instructor-image {
    margin-bottom: 2rem;
  }
  
  .benefits-content {
    grid-template-columns: 1fr;
  }
  
  .faq-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .hero h1 {
    font-size: 2.5rem;
  }
  
  .hero-cta {
    flex-direction: column;
  }
  
  .benefit-item {
    flex-direction: column;
    text-align: center;
  }
  
  .pricing-card {
    padding: 2rem 1.5rem;
  }
  
  .cta-buttons {
    flex-direction: column;
  }
}

@media (max-width: 576px) {
  .hero h1 {
    font-size: 2rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
  
  .info-item {
    width: 100%;
    justify-content: center;
  }
}
