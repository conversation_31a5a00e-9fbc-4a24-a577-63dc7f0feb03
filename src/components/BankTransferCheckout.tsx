import React, { useState } from 'react';
import { FaBank, FaCopy, FaWhatsapp, FaEnvelope, FaCheckCircle } from 'react-icons/fa';
import './BankTransferCheckout.css';

interface BankTransferCheckoutProps {
  onSuccess: () => void;
  onCancel: () => void;
}

const BankTransferCheckout: React.FC<BankTransferCheckoutProps> = ({ onSuccess, onCancel }) => {
  const [step, setStep] = useState<'form' | 'payment-info' | 'confirmation'>('form');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [customerInfo, setCustomerInfo] = useState({
    name: '',
    email: '',
    phone: ''
  });

  // Información bancaria desde variables de entorno
  const bankInfo = {
    bank: import.meta.env.VITE_BANK_NAME || 'BBVA México',
    accountNumber: import.meta.env.VITE_BANK_ACCOUNT || '**********',
    clabe: import.meta.env.VITE_BANK_CLABE || '0**********1234567',
    accountHolder: import.meta.env.VITE_BANK_HOLDER || 'Will de la Vega',
    concept: 'Curso IA 2024'
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    // Validar información del cliente
    if (!customerInfo.name || !customerInfo.email || !customerInfo.phone) {
      setError('Por favor completa todos los campos requeridos');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Simular procesamiento
      await new Promise(resolve => setTimeout(resolve, 1000));
      setStep('payment-info');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error procesando la información');
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text).then(() => {
      alert(`✅ ${label} copiado al portapapeles`);
    }).catch(() => {
      alert(`❌ No se pudo copiar. ${label}: ${text}`);
    });
  };

  const handleWhatsApp = () => {
    const message = encodeURIComponent(
      `Hola Will, quiero inscribirme al Curso de Inteligencia Artificial 2024.

📝 Mis datos:
• Nombre: ${customerInfo.name}
• Email: ${customerInfo.email}
• Teléfono: ${customerInfo.phone}

💰 Realizaré el depósito de $500 MXN y te enviaré el comprobante.

¡Gracias!`
    );
    window.open(`https://wa.me/529996406775?text=${message}`, '_blank');
  };

  const handleEmail = () => {
    const subject = encodeURIComponent('Inscripción Curso IA 2024 - Pago por Transferencia');
    const body = encodeURIComponent(
      `Hola Will,

Quiero inscribirme al Curso de Inteligencia Artificial 2024.

Mis datos:
• Nombre: ${customerInfo.name}
• Email: ${customerInfo.email}
• Teléfono: ${customerInfo.phone}

Realizaré el depósito bancario de $500 MXN y te enviaré el comprobante de pago.

Saludos,
${customerInfo.name}`
    );
    window.location.href = `mailto:<EMAIL>?subject=${subject}&body=${body}`;
  };

  const handleConfirmPayment = () => {
    setStep('confirmation');
    // Simular confirmación y redirigir después de 3 segundos
    setTimeout(() => {
      onSuccess();
    }, 3000);
  };

  if (step === 'form') {
    return (
      <div className="bank-checkout-container">
        <div className="bank-checkout-header">
          <FaBank className="bank-icon" />
          <h2>Pago por Transferencia Bancaria</h2>
          <p className="product-info">
            {import.meta.env.VITE_PRODUCT_NAME || 'Curso de Inteligencia Artificial 2024'}
          </p>
          <p className="price-info">
            $500 MXN
          </p>
        </div>

        <form onSubmit={handleSubmit} className="bank-checkout-form">
          <div className="customer-info">
            <h3>📝 Información de contacto</h3>
            <div className="form-group">
              <label htmlFor="name">Nombre completo *</label>
              <input
                type="text"
                id="name"
                required
                value={customerInfo.name}
                onChange={(e) => setCustomerInfo({...customerInfo, name: e.target.value})}
                placeholder="Tu nombre completo"
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="email">Email *</label>
              <input
                type="email"
                id="email"
                required
                value={customerInfo.email}
                onChange={(e) => setCustomerInfo({...customerInfo, email: e.target.value})}
                placeholder="<EMAIL>"
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="phone">Teléfono *</label>
              <input
                type="tel"
                id="phone"
                required
                value={customerInfo.phone}
                onChange={(e) => setCustomerInfo({...customerInfo, phone: e.target.value})}
                placeholder="+52 55 1234 5678"
              />
            </div>
          </div>

          <div className="payment-info-preview">
            <h3>🏦 Método de pago</h3>
            <div className="payment-method-card">
              <FaBank className="method-icon" />
              <div className="method-details">
                <h4>Transferencia o Depósito Bancario</h4>
                <p>Recibirás los datos bancarios para realizar tu pago</p>
                <ul>
                  <li>✅ Transferencia SPEI (inmediata)</li>
                  <li>✅ Depósito en sucursal</li>
                  <li>✅ Banca en línea</li>
                  <li>✅ Confirmación por WhatsApp</li>
                </ul>
              </div>
            </div>
          </div>

          {error && (
            <div className="error-message">
              ❌ {error}
            </div>
          )}

          <div className="bank-checkout-actions">
            <button
              type="button"
              onClick={onCancel}
              className="btn btn-secondary"
              disabled={loading}
            >
              Cancelar
            </button>
            
            <button
              type="submit"
              className="btn btn-primary"
              disabled={loading}
            >
              {loading ? 'Procesando...' : 'Continuar al Pago 🏦'}
            </button>
          </div>
        </form>
      </div>
    );
  }

  if (step === 'payment-info') {
    return (
      <div className="bank-checkout-container">
        <div className="bank-checkout-header">
          <FaBank className="bank-icon" />
          <h2>Datos para tu Transferencia</h2>
          <p className="customer-name">Hola {customerInfo.name} 👋</p>
        </div>

        <div className="bank-details">
          <h3>🏦 Información Bancaria</h3>
          
          <div className="bank-detail-item">
            <label>Banco:</label>
            <div className="detail-value">
              <span>{bankInfo.bank}</span>
              <button onClick={() => copyToClipboard(bankInfo.bank, 'Banco')} className="copy-btn">
                <FaCopy />
              </button>
            </div>
          </div>

          <div className="bank-detail-item">
            <label>Número de cuenta:</label>
            <div className="detail-value">
              <span>{bankInfo.accountNumber}</span>
              <button onClick={() => copyToClipboard(bankInfo.accountNumber, 'Número de cuenta')} className="copy-btn">
                <FaCopy />
              </button>
            </div>
          </div>

          <div className="bank-detail-item">
            <label>CLABE interbancaria:</label>
            <div className="detail-value">
              <span>{bankInfo.clabe}</span>
              <button onClick={() => copyToClipboard(bankInfo.clabe, 'CLABE')} className="copy-btn">
                <FaCopy />
              </button>
            </div>
          </div>

          <div className="bank-detail-item">
            <label>Titular:</label>
            <div className="detail-value">
              <span>{bankInfo.accountHolder}</span>
              <button onClick={() => copyToClipboard(bankInfo.accountHolder, 'Titular')} className="copy-btn">
                <FaCopy />
              </button>
            </div>
          </div>

          <div className="bank-detail-item">
            <label>Monto:</label>
            <div className="detail-value amount">
              <span>$500.00 MXN</span>
              <button onClick={() => copyToClipboard('500.00', 'Monto')} className="copy-btn">
                <FaCopy />
              </button>
            </div>
          </div>

          <div className="bank-detail-item">
            <label>Concepto:</label>
            <div className="detail-value">
              <span>{bankInfo.concept} - {customerInfo.name}</span>
              <button onClick={() => copyToClipboard(`${bankInfo.concept} - ${customerInfo.name}`, 'Concepto')} className="copy-btn">
                <FaCopy />
              </button>
            </div>
          </div>
        </div>

        <div className="instructions">
          <h3>📋 Instrucciones</h3>
          <ol>
            <li>Realiza la transferencia con los datos de arriba</li>
            <li>Toma una foto del comprobante de pago</li>
            <li>Envíanos el comprobante por WhatsApp o email</li>
            <li>Confirmaremos tu pago en máximo 2 horas</li>
            <li>Recibirás acceso al curso inmediatamente</li>
          </ol>
        </div>

        <div className="contact-options">
          <h3>📱 Enviar comprobante</h3>
          <div className="contact-buttons">
            <button onClick={handleWhatsApp} className="btn btn-whatsapp">
              <FaWhatsapp />
              WhatsApp
            </button>
            
            <button onClick={handleEmail} className="btn btn-email">
              <FaEnvelope />
              Email
            </button>
          </div>
        </div>

        <div className="bank-checkout-actions">
          <button
            onClick={() => setStep('form')}
            className="btn btn-secondary"
          >
            ← Volver
          </button>
          
          <button
            onClick={handleConfirmPayment}
            className="btn btn-success"
          >
            Ya realicé el pago ✅
          </button>
        </div>
      </div>
    );
  }

  if (step === 'confirmation') {
    return (
      <div className="bank-checkout-container">
        <div className="confirmation-content">
          <FaCheckCircle className="success-icon" />
          <h2>¡Solicitud Recibida!</h2>
          <p>Gracias {customerInfo.name}, hemos recibido tu solicitud de inscripción.</p>
          
          <div className="next-steps">
            <h3>📋 Próximos pasos:</h3>
            <ul>
              <li>✅ Confirmaremos tu pago en máximo 2 horas</li>
              <li>✅ Te enviaremos acceso al curso por email</li>
              <li>✅ Serás agregado al grupo de WhatsApp</li>
              <li>✅ Recibirás el material del curso</li>
            </ul>
          </div>

          <p className="redirect-message">
            Serás redirigido a la página de confirmación en unos segundos...
          </p>
        </div>
      </div>
    );
  }

  return null;
};

export default BankTransferCheckout;
