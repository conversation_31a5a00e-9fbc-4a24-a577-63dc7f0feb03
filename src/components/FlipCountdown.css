.countdown-container {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  padding: 3rem 2rem;
  border-radius: 1.5rem;
  text-align: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.countdown-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 50%, rgba(37, 99, 235, 0.1) 0%, transparent 70%);
  pointer-events: none;
}

.countdown-header {
  margin-bottom: 2.5rem;
  position: relative;
  z-index: 2;
}

.countdown-title {
  color: white;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: -0.02em;
}

.countdown-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.25rem;
  font-weight: 500;
  margin: 0;
}

.countdown-timer {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2.5rem;
  position: relative;
  z-index: 2;
}

.flip-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.flip-card-inner {
  position: relative;
  width: 80px;
  height: 100px;
  perspective: 1000px;
}

.flip-card-front,
.flip-card-back {
  position: absolute;
  width: 100%;
  height: 50px;
  background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
  border: 2px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.75rem;
  font-weight: 700;
  color: white;
  font-family: 'Inter', monospace;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  overflow: hidden;
}

.flip-card-top {
  top: 0;
  border-radius: 8px 8px 0 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.flip-card-bottom {
  bottom: 0;
  border-radius: 0 0 8px 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.flip-animation {
  animation: flip 0.6s ease-in-out;
  z-index: 10;
}

@keyframes flip {
  0% {
    transform: rotateX(0deg);
  }
  50% {
    transform: rotateX(-90deg);
  }
  100% {
    transform: rotateX(0deg);
  }
}

.flip-card-front.flip-animation {
  transform-origin: bottom;
  animation: flipTop 0.3s ease-in forwards;
}

.flip-card-back.flip-animation {
  transform-origin: top;
  animation: flipBottom 0.3s ease-out 0.3s forwards;
  transform: rotateX(90deg);
}

@keyframes flipTop {
  0% {
    transform: rotateX(0deg);
  }
  100% {
    transform: rotateX(-90deg);
  }
}

@keyframes flipBottom {
  0% {
    transform: rotateX(90deg);
  }
  100% {
    transform: rotateX(0deg);
  }
}

.flip-card-label {
  margin-top: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.countdown-separator {
  color: white;
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.countdown-info {
  position: relative;
  z-index: 2;
}

.course-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.detail-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.detail-icon {
  font-size: 1.5rem;
}

.detail-text {
  color: white;
  font-weight: 500;
  font-size: 1rem;
}

.urgency-message {
  background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
  padding: 1.5rem;
  border-radius: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.urgency-message::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shine 2s infinite;
}

@keyframes shine {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.urgency-message p {
  color: white;
  margin: 0;
  font-size: 1.1rem;
  line-height: 1.5;
  position: relative;
  z-index: 2;
}

/* Responsive */
@media (max-width: 768px) {
  .countdown-container {
    padding: 2rem 1rem;
  }
  
  .countdown-title {
    font-size: 2rem;
  }
  
  .countdown-subtitle {
    font-size: 1.1rem;
  }
  
  .countdown-timer {
    gap: 0.5rem;
  }
  
  .flip-card-inner {
    width: 60px;
    height: 80px;
  }
  
  .flip-card-front,
  .flip-card-back {
    height: 40px;
    font-size: 1.5rem;
  }
  
  .countdown-separator {
    font-size: 1.5rem;
    margin: 0 0.25rem;
  }
  
  .course-details {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
  
  .detail-item {
    padding: 0.75rem;
  }
  
  .detail-text {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .countdown-timer {
    flex-wrap: wrap;
    gap: 0.75rem;
  }
  
  .flip-card-inner {
    width: 50px;
    height: 70px;
  }
  
  .flip-card-front,
  .flip-card-back {
    height: 35px;
    font-size: 1.25rem;
  }
  
  .countdown-separator {
    display: none;
  }
}
