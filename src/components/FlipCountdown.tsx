import React, { useState, useEffect } from 'react';
import './FlipCountdown.css';

interface FlipCountdownProps {
  targetDate: Date;
  title?: string;
  subtitle?: string;
}

interface TimeLeft {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
}

const FlipCountdown: React.FC<FlipCountdownProps> = ({ 
  targetDate, 
  title = "¡El curso inicia en:",
  subtitle = "Próximo lunes a las 7:00 PM"
}) => {
  const [timeLeft, setTimeLeft] = useState<TimeLeft>({ days: 0, hours: 0, minutes: 0, seconds: 0 });
  const [prevTimeLeft, setPrevTimeLeft] = useState<TimeLeft>({ days: 0, hours: 0, minutes: 0, seconds: 0 });

  const calculateTimeLeft = (): TimeLeft => {
    const difference = targetDate.getTime() - new Date().getTime();
    
    if (difference > 0) {
      return {
        days: Math.floor(difference / (1000 * 60 * 60 * 24)),
        hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
        minutes: Math.floor((difference / 1000 / 60) % 60),
        seconds: Math.floor((difference / 1000) % 60)
      };
    }
    
    return { days: 0, hours: 0, minutes: 0, seconds: 0 };
  };

  useEffect(() => {
    const timer = setInterval(() => {
      const newTimeLeft = calculateTimeLeft();
      setPrevTimeLeft(timeLeft);
      setTimeLeft(newTimeLeft);
    }, 1000);

    // Calcular tiempo inicial
    setTimeLeft(calculateTimeLeft());

    return () => clearInterval(timer);
  }, [targetDate, timeLeft]);

  const formatNumber = (num: number): string => {
    return num.toString().padStart(2, '0');
  };

  const FlipCard: React.FC<{ 
    current: number; 
    previous: number; 
    label: string;
    shouldFlip: boolean;
  }> = ({ current, previous, label, shouldFlip }) => {
    return (
      <div className="flip-card">
        <div className="flip-card-inner">
          {/* Parte superior */}
          <div className="flip-card-front flip-card-top">
            <span>{formatNumber(current)}</span>
          </div>
          
          {/* Parte inferior */}
          <div className="flip-card-back flip-card-bottom">
            <span>{formatNumber(current)}</span>
          </div>
          
          {/* Animación de flip */}
          {shouldFlip && (
            <>
              <div className="flip-card-front flip-card-top flip-animation">
                <span>{formatNumber(previous)}</span>
              </div>
              <div className="flip-card-back flip-card-bottom flip-animation">
                <span>{formatNumber(current)}</span>
              </div>
            </>
          )}
        </div>
        
        <div className="flip-card-label">{label}</div>
      </div>
    );
  };

  const shouldFlip = (current: number, previous: number): boolean => {
    return current !== previous;
  };

  return (
    <div className="countdown-container">
      <div className="countdown-header">
        <h2 className="countdown-title">{title}</h2>
        <p className="countdown-subtitle">{subtitle}</p>
      </div>
      
      <div className="countdown-timer">
        <FlipCard 
          current={timeLeft.days} 
          previous={prevTimeLeft.days}
          label="Días"
          shouldFlip={shouldFlip(timeLeft.days, prevTimeLeft.days)}
        />
        
        <div className="countdown-separator">:</div>
        
        <FlipCard 
          current={timeLeft.hours} 
          previous={prevTimeLeft.hours}
          label="Horas"
          shouldFlip={shouldFlip(timeLeft.hours, prevTimeLeft.hours)}
        />
        
        <div className="countdown-separator">:</div>
        
        <FlipCard 
          current={timeLeft.minutes} 
          previous={prevTimeLeft.minutes}
          label="Minutos"
          shouldFlip={shouldFlip(timeLeft.minutes, prevTimeLeft.minutes)}
        />
        
        <div className="countdown-separator">:</div>
        
        <FlipCard 
          current={timeLeft.seconds} 
          previous={prevTimeLeft.seconds}
          label="Segundos"
          shouldFlip={shouldFlip(timeLeft.seconds, prevTimeLeft.seconds)}
        />
      </div>
      
      <div className="countdown-info">
        <div className="course-details">
          <div className="detail-item">
            <span className="detail-icon">📅</span>
            <span className="detail-text">Próximo lunes</span>
          </div>
          <div className="detail-item">
            <span className="detail-icon">🕰️</span>
            <span className="detail-text">7:00 PM (México)</span>
          </div>
          <div className="detail-item">
            <span className="detail-icon">⏱️</span>
            <span className="detail-text">7 días intensivos</span>
          </div>
          <div className="detail-item">
            <span className="detail-icon">👨‍💻</span>
            <span className="detail-text">En vivo con Will</span>
          </div>
        </div>
        
        <div className="urgency-message">
          <p>🔥 <strong>¡Últimas plazas disponibles!</strong> Solo quedan pocos cupos para garantizar atención personalizada.</p>
        </div>
      </div>
    </div>
  );
};

export default FlipCountdown;
