.bank-checkout-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem;
  background: var(--bg-primary);
  border-radius: 1rem;
  box-shadow: var(--shadow-medium);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.bank-checkout-header {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 2px solid #10b981;
}

.bank-icon {
  font-size: 3rem;
  color: #10b981;
  margin-bottom: 1rem;
}

.bank-checkout-header h2 {
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-size: 2rem;
  font-weight: 600;
}

.product-info {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.price-info {
  font-size: 2rem;
  font-weight: 700;
  color: #10b981;
  margin: 0;
}

.customer-name {
  font-size: 1.25rem;
  color: var(--text-primary);
  font-weight: 500;
}

.bank-checkout-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.customer-info h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
  font-weight: 500;
}

.form-group input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e2e8f0;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-family: 'Inter', sans-serif;
  transition: border-color 0.3s ease;
}

.form-group input:focus {
  outline: none;
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.payment-info-preview {
  background: rgba(16, 185, 129, 0.05);
  padding: 1.5rem;
  border-radius: 1rem;
  border: 1px solid rgba(16, 185, 129, 0.1);
}

.payment-info-preview h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.payment-method-card {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 0.75rem;
  border: 2px solid #10b981;
}

.method-icon {
  font-size: 2rem;
  color: #10b981;
  margin-top: 0.25rem;
}

.method-details h4 {
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.method-details p {
  color: var(--text-secondary);
  margin-bottom: 1rem;
  line-height: 1.5;
}

.method-details ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.method-details li {
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
}

.bank-details {
  margin: 2rem 0;
}

.bank-details h3 {
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  text-align: center;
}

.bank-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  margin-bottom: 0.75rem;
  background: rgba(16, 185, 129, 0.05);
  border-radius: 0.75rem;
  border: 1px solid rgba(16, 185, 129, 0.1);
}

.bank-detail-item label {
  font-weight: 600;
  color: var(--text-primary);
  min-width: 140px;
}

.detail-value {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
  justify-content: flex-end;
}

.detail-value span {
  color: var(--text-secondary);
  font-family: 'Courier New', monospace;
  font-weight: 500;
}

.detail-value.amount span {
  font-size: 1.25rem;
  font-weight: 700;
  color: #10b981;
}

.copy-btn {
  background: #10b981;
  color: white;
  border: none;
  border-radius: 0.5rem;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.copy-btn:hover {
  background: #059669;
  transform: scale(1.05);
}

.instructions {
  background: rgba(245, 158, 11, 0.05);
  padding: 1.5rem;
  border-radius: 1rem;
  border: 1px solid rgba(245, 158, 11, 0.1);
  margin: 2rem 0;
}

.instructions h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.instructions ol {
  padding-left: 1.5rem;
  margin: 0;
}

.instructions li {
  color: var(--text-secondary);
  margin-bottom: 0.75rem;
  line-height: 1.5;
}

.contact-options {
  margin: 2rem 0;
  text-align: center;
}

.contact-options h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.contact-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.contact-buttons .btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-family: 'Inter', sans-serif;
}

.btn-whatsapp {
  background: #25D366;
  color: white;
}

.btn-whatsapp:hover {
  background: #128C7E;
  transform: translateY(-2px);
}

.btn-email {
  background: var(--primary-color);
  color: white;
}

.btn-email:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
}

.error-message {
  background: #fee2e2;
  color: #dc2626;
  padding: 1rem;
  border-radius: 0.5rem;
  border: 1px solid #fecaca;
  margin: 1rem 0;
  font-weight: 500;
}

.bank-checkout-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

.bank-checkout-actions .btn {
  flex: 1;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  font-family: 'Inter', sans-serif;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-primary {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: var(--shadow-medium);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-strong);
}

.btn-secondary {
  background: #6b7280;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #4b5563;
  transform: translateY(-2px);
}

.btn-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: var(--shadow-medium);
}

.btn-success:hover {
  background: #059669;
  transform: translateY(-2px);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Confirmación */
.confirmation-content {
  text-align: center;
  padding: 2rem;
}

.success-icon {
  font-size: 4rem;
  color: #10b981;
  margin-bottom: 1.5rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.confirmation-content h2 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 2rem;
}

.confirmation-content p {
  color: var(--text-secondary);
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.next-steps {
  background: rgba(16, 185, 129, 0.05);
  padding: 1.5rem;
  border-radius: 1rem;
  border: 1px solid rgba(16, 185, 129, 0.1);
  margin: 2rem 0;
  text-align: left;
}

.next-steps h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  text-align: center;
}

.next-steps ul {
  list-style: none;
  padding: 0;
}

.next-steps li {
  color: var(--text-secondary);
  margin-bottom: 0.75rem;
  line-height: 1.5;
}

.redirect-message {
  font-style: italic;
  color: var(--text-secondary);
  font-size: 0.95rem;
}

/* Responsive */
@media (max-width: 768px) {
  .bank-checkout-container {
    margin: 1rem;
    padding: 1.5rem;
  }
  
  .bank-detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .detail-value {
    justify-content: flex-start;
    width: 100%;
  }
  
  .contact-buttons {
    flex-direction: column;
  }
  
  .bank-checkout-actions {
    flex-direction: column;
  }
}
