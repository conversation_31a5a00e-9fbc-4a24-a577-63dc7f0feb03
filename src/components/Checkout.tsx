import React, { useState } from 'react';
import './Checkout.css';

// ✅ STRIPE ACTIVADO - Descomenta estas líneas después de instalar dependencias
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  CardElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';

// Cargar Stripe con tu clave pública
const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY);

interface CheckoutFormProps {
  onSuccess: () => void;
  onCancel: () => void;
}

const CheckoutForm: React.FC<CheckoutFormProps> = ({ onSuccess, onCancel }) => {
  // ✅ STRIPE ACTIVADO
  const stripe = useStripe();
  const elements = useElements();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [paymentMethod, setPaymentMethod] = useState<'card' | 'oxxo'>('card');
  const [customerInfo, setCustomerInfo] = useState({
    name: '',
    email: '',
    phone: ''
  });

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      setError('Stripe no está cargado correctamente. Verifica tu clave pública.');
      return;
    }

    // Validar información del cliente
    if (!customerInfo.name || !customerInfo.email || !customerInfo.phone) {
      setError('Por favor completa todos los campos requeridos');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      if (paymentMethod === 'card') {
        await handleCardPayment();
      } else {
        await handleOxxoPayment();
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error procesando el pago');
      setLoading(false);
    }
  };

  // ✅ FUNCIONES DE PAGO ACTIVADAS
  const handleCardPayment = async () => {
    const cardElement = elements!.getElement(CardElement);

    if (!cardElement) {
      throw new Error('No se pudo cargar el formulario de tarjeta');
    }

    // Crear Payment Intent
    const response = await fetch('/api/create-payment-intent', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        amount: parseInt(import.meta.env.VITE_PRODUCT_PRICE),
        currency: import.meta.env.VITE_CURRENCY,
        payment_method_types: ['card'],
        customer_info: customerInfo
      }),
    });

    const { client_secret } = await response.json();

    // Confirmar pago
    const result = await stripe!.confirmCardPayment(client_secret, {
      payment_method: {
        card: cardElement,
        billing_details: {
          name: customerInfo.name,
          email: customerInfo.email,
          phone: customerInfo.phone,
        },
      },
    });

    if (result.error) {
      throw new Error(result.error.message);
    } else {
      onSuccess();
    }
  };

  const handleOxxoPayment = async () => {
    // Crear Payment Intent para OXXO
    const response = await fetch('/api/create-payment-intent', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        amount: parseInt(import.meta.env.VITE_PRODUCT_PRICE),
        currency: import.meta.env.VITE_CURRENCY,
        payment_method_types: ['oxxo'],
        customer_info: customerInfo
      }),
    });

    const { client_secret } = await response.json();

    // Confirmar pago OXXO
    const result = await stripe!.confirmOxxoPayment(client_secret, {
      payment_method: {
        billing_details: {
          name: customerInfo.name,
          email: customerInfo.email,
          phone: customerInfo.phone,
        },
      },
    });

    if (result.error) {
      throw new Error(result.error.message);
    } else {
      // Para OXXO, redirigir a página con instrucciones
      window.location.href = `/oxxo-instructions?payment_intent=${result.paymentIntent?.id}`;
    }
  };

  // Configuración temporal sin Stripe
  const cardElementOptions = {
    style: {
      base: {
        fontSize: '16px',
        color: '#424770',
        '::placeholder': {
          color: '#aab7c4',
        },
      },
    },
  };

  return (
    <div className="checkout-container">
      <div className="checkout-header">
        <h2>Finalizar Compra</h2>
        <p className="product-info">
          {import.meta.env.VITE_PRODUCT_NAME}
        </p>
        <p className="price-info">
          ${(parseInt(import.meta.env.VITE_PRODUCT_PRICE) / 100).toLocaleString('es-MX')} MXN
        </p>
      </div>

      <form onSubmit={handleSubmit} className="checkout-form">
        {/* Información del cliente */}
        <div className="customer-info">
          <h3>Información de contacto</h3>
          <div className="form-group">
            <label htmlFor="name">Nombre completo *</label>
            <input
              type="text"
              id="name"
              required
              value={customerInfo.name}
              onChange={(e) => setCustomerInfo({...customerInfo, name: e.target.value})}
              placeholder="Tu nombre completo"
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="email">Email *</label>
            <input
              type="email"
              id="email"
              required
              value={customerInfo.email}
              onChange={(e) => setCustomerInfo({...customerInfo, email: e.target.value})}
              placeholder="<EMAIL>"
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="phone">Teléfono *</label>
            <input
              type="tel"
              id="phone"
              required
              value={customerInfo.phone}
              onChange={(e) => setCustomerInfo({...customerInfo, phone: e.target.value})}
              placeholder="+52 55 1234 5678"
            />
          </div>
        </div>

        {/* Método de pago */}
        <div className="payment-methods">
          <h3>Método de pago</h3>
          <div className="payment-options">
            <label className={`payment-option ${paymentMethod === 'card' ? 'selected' : ''}`}>
              <input
                type="radio"
                value="card"
                checked={paymentMethod === 'card'}
                onChange={(e) => setPaymentMethod(e.target.value as 'card')}
              />
              <span>💳 Tarjeta de crédito/débito</span>
            </label>
            
            <label className={`payment-option ${paymentMethod === 'oxxo' ? 'selected' : ''}`}>
              <input
                type="radio"
                value="oxxo"
                checked={paymentMethod === 'oxxo'}
                onChange={(e) => setPaymentMethod(e.target.value as 'oxxo')}
              />
              <span>🏪 OXXO (Pago en efectivo)</span>
            </label>
          </div>
        </div>

        {/* Formulario de tarjeta - ✅ STRIPE REAL */}
        {paymentMethod === 'card' && (
          <div className="card-element-container">
            <label htmlFor="card-element">Información de la tarjeta</label>
            <CardElement id="card-element" options={cardElementOptions} />
            <p className="stripe-note">
              🔒 <strong>Seguro:</strong> Procesado por Stripe. Tus datos están protegidos.
            </p>
          </div>
        )}

        {/* Información OXXO */}
        {paymentMethod === 'oxxo' && (
          <div className="oxxo-info">
            <p>📍 <strong>Pago en OXXO:</strong></p>
            <ul>
              <li>Recibirás un código de barras por email</li>
              <li>Ve a cualquier OXXO y presenta el código</li>
              <li>Paga en efectivo</li>
              <li>Tu acceso se activará automáticamente</li>
            </ul>
          </div>
        )}

        {error && (
          <div className="error-message">
            ❌ {error}
          </div>
        )}

        <div className="checkout-actions">
          <button
            type="button"
            onClick={onCancel}
            className="btn btn-secondary"
            disabled={loading}
          >
            Cancelar
          </button>
          
          <button
            type="submit"
            className="btn btn-primary"
            disabled={loading || !stripe}
          >
            {loading ? 'Procesando...' :
             paymentMethod === 'card' ? 'Pagar Ahora' : 'Generar Código OXXO'}
          </button>
        </div>
      </form>
    </div>
  );
};

interface CheckoutProps {
  onSuccess: () => void;
  onCancel: () => void;
}

const Checkout: React.FC<CheckoutProps> = ({ onSuccess, onCancel }) => {
  // ✅ STRIPE COMPLETAMENTE ACTIVADO
  return (
    <Elements stripe={stripePromise}>
      <CheckoutForm onSuccess={onSuccess} onCancel={onCancel} />
    </Elements>
  );
};

export default Checkout;
