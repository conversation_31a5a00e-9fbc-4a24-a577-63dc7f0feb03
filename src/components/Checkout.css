.checkout-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem;
  background: var(--bg-primary);
  border-radius: 1rem;
  box-shadow: var(--shadow-medium);
  border: 1px solid rgba(37, 99, 235, 0.1);
}

.checkout-header {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 2px solid var(--primary-color);
}

.checkout-header h2 {
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-size: 2rem;
  font-weight: 600;
}

.product-info {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.price-info {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin: 0;
}

.checkout-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.customer-info h3,
.payment-methods h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
  font-weight: 500;
}

.form-group input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e2e8f0;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-family: 'Inter', sans-serif;
  transition: border-color 0.3s ease;
}

.form-group input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.payment-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.payment-option {
  display: flex;
  align-items: center;
  padding: 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--bg-secondary);
}

.payment-option:hover {
  border-color: var(--primary-color);
  background: rgba(37, 99, 235, 0.05);
}

.payment-option.selected {
  border-color: var(--primary-color);
  background: rgba(37, 99, 235, 0.1);
}

.payment-option input[type="radio"] {
  margin-right: 0.75rem;
  width: 1.25rem;
  height: 1.25rem;
}

.payment-option span {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--text-primary);
}

.card-element-container {
  margin: 1.5rem 0;
}

.card-element-container label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
  font-weight: 500;
}

.card-element-container .StripeElement {
  padding: 0.75rem;
  border: 2px solid #e2e8f0;
  border-radius: 0.5rem;
  background: white;
  transition: border-color 0.3s ease;
}

.card-element-container .StripeElement:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Estilos para inputs de demostración */
.card-demo-inputs {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.card-input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e2e8f0;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-family: 'Inter', sans-serif;
  background: #f8f9fa;
  color: #6b7280;
}

.card-row {
  display: flex;
  gap: 1rem;
}

.card-input-small {
  flex: 1;
  padding: 0.75rem;
  border: 2px solid #e2e8f0;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-family: 'Inter', sans-serif;
  background: #f8f9fa;
  color: #6b7280;
}

.demo-note {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
  padding: 1rem;
  border-radius: 0.5rem;
  border: 1px solid rgba(245, 158, 11, 0.2);
  margin-top: 1rem;
  font-size: 0.9rem;
  line-height: 1.5;
}

.stripe-note {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  padding: 1rem;
  border-radius: 0.5rem;
  border: 1px solid rgba(16, 185, 129, 0.2);
  margin-top: 1rem;
  font-size: 0.9rem;
  line-height: 1.5;
}

.oxxo-info {
  background: linear-gradient(135deg, #f59e0b 0%, #fb923c 100%);
  color: white;
  padding: 1.5rem;
  border-radius: 0.75rem;
  margin: 1rem 0;
}

.oxxo-info p {
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.oxxo-info ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.oxxo-info li {
  margin-bottom: 0.5rem;
  padding-left: 1.5rem;
  position: relative;
}

.oxxo-info li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: white;
  font-weight: bold;
}

.error-message {
  background: #fee2e2;
  color: #dc2626;
  padding: 1rem;
  border-radius: 0.5rem;
  border: 1px solid #fecaca;
  margin: 1rem 0;
  font-weight: 500;
}

.checkout-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

.checkout-actions .btn {
  flex: 1;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  font-family: 'Inter', sans-serif;
}

.checkout-actions .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.checkout-actions .btn-primary {
  background: var(--bg-gradient-hero);
  color: white;
  box-shadow: var(--shadow-medium);
}

.checkout-actions .btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-strong);
}

.checkout-actions .btn-secondary {
  background: #6b7280;
  color: white;
}

.checkout-actions .btn-secondary:hover:not(:disabled) {
  background: #4b5563;
  transform: translateY(-2px);
}

/* Responsive */
@media (max-width: 768px) {
  .checkout-container {
    margin: 1rem;
    padding: 1.5rem;
  }
  
  .checkout-header h2 {
    font-size: 1.75rem;
  }
  
  .price-info {
    font-size: 1.75rem;
  }
  
  .checkout-actions {
    flex-direction: column;
  }
  
  .payment-options {
    gap: 0.75rem;
  }
  
  .payment-option {
    padding: 0.75rem;
  }
}
