// Servidor Express para manejar pagos con Stripe
// Ejecutar con: node server/stripe-server.js

const express = require('express');
const cors = require('cors');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors({
  origin: [
    'http://localhost:3000',
    'https://cursos.willdelavega.com'
  ]
}));
app.use(express.json());

// Endpoint para crear Payment Intent
app.post('/api/create-payment-intent', async (req, res) => {
  try {
    const { amount, currency, payment_method_types, customer_info } = req.body;

    // Validar datos requeridos
    if (!amount || !currency || !payment_method_types || !customer_info) {
      return res.status(400).json({
        error: 'Faltan datos requeridos'
      });
    }

    // Configuración base del Payment Intent
    const paymentIntentData = {
      amount: amount, // En centavos (50000 = $500 MXN)
      currency: currency, // 'mxn'
      payment_method_types: payment_method_types, // ['card'] o ['oxxo']
      metadata: {
        customer_name: customer_info.name,
        customer_email: customer_info.email,
        customer_phone: customer_info.phone,
        product: 'Curso de Inteligencia Artificial 2024',
        instructor: 'Will de la Vega'
      },
      receipt_email: customer_info.email,
      description: 'Curso de Inteligencia Artificial 2024 - Will de la Vega'
    };

    // Configuración específica para OXXO
    if (payment_method_types.includes('oxxo')) {
      paymentIntentData.payment_method_options = {
        oxxo: {
          expires_after_days: 3 // El código OXXO expira en 3 días
        }
      };
    }

    // Crear el Payment Intent
    const paymentIntent = await stripe.paymentIntents.create(paymentIntentData);

    res.json({
      client_secret: paymentIntent.client_secret,
      payment_intent_id: paymentIntent.id
    });

  } catch (error) {
    console.error('Error creando Payment Intent:', error);
    res.status(500).json({
      error: 'Error interno del servidor',
      message: error.message
    });
  }
});

// Endpoint para confirmar pago exitoso
app.post('/api/confirm-payment', async (req, res) => {
  try {
    const { payment_intent_id } = req.body;

    // Recuperar el Payment Intent
    const paymentIntent = await stripe.paymentIntents.retrieve(payment_intent_id);

    if (paymentIntent.status === 'succeeded') {
      // Aquí puedes agregar lógica adicional:
      // - Enviar email de confirmación
      // - Agregar al estudiante a la base de datos
      // - Enviar acceso al curso
      // - Agregar a grupo de WhatsApp

      console.log('Pago exitoso:', {
        id: paymentIntent.id,
        amount: paymentIntent.amount,
        customer: paymentIntent.metadata
      });

      res.json({
        success: true,
        payment_intent: paymentIntent
      });
    } else {
      res.status(400).json({
        success: false,
        status: paymentIntent.status
      });
    }

  } catch (error) {
    console.error('Error confirmando pago:', error);
    res.status(500).json({
      error: 'Error confirmando pago',
      message: error.message
    });
  }
});

// Webhook para recibir eventos de Stripe
app.post('/api/webhook', express.raw({type: 'application/json'}), (req, res) => {
  const sig = req.headers['stripe-signature'];
  let event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, process.env.STRIPE_WEBHOOK_SECRET);
  } catch (err) {
    console.error('Error verificando webhook:', err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  // Manejar eventos específicos
  switch (event.type) {
    case 'payment_intent.succeeded':
      const paymentIntent = event.data.object;
      console.log('Pago exitoso recibido via webhook:', paymentIntent.id);
      
      // Aquí puedes agregar lógica para:
      // - Enviar email de confirmación
      // - Activar acceso al curso
      // - Actualizar base de datos
      // - Notificar al instructor
      
      break;

    case 'payment_intent.payment_failed':
      const failedPayment = event.data.object;
      console.log('Pago fallido:', failedPayment.id);
      
      // Lógica para manejar pagos fallidos
      // - Enviar email de soporte
      // - Notificar al equipo
      
      break;

    case 'payment_method.attached':
      const paymentMethod = event.data.object;
      console.log('Método de pago adjuntado:', paymentMethod.id);
      break;

    default:
      console.log(`Evento no manejado: ${event.type}`);
  }

  res.json({received: true});
});

// Endpoint de salud
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    service: 'Stripe Payment Server'
  });
});

// Endpoint para obtener información del producto
app.get('/api/product-info', (req, res) => {
  res.json({
    name: process.env.VITE_PRODUCT_NAME || 'Curso de Inteligencia Artificial 2024',
    price: parseInt(process.env.VITE_PRODUCT_PRICE) || 50000,
    currency: process.env.VITE_CURRENCY || 'mxn',
    instructor: 'Will de la Vega',
    duration: '7 días intensivos',
    includes: [
      '7 sesiones en vivo',
      'Grabaciones por 6 meses',
      'Material descargable',
      'Certificado de participación',
      'Grupo privado de WhatsApp',
      'Soporte directo'
    ]
  });
});

// Manejo de errores global
app.use((error, req, res, next) => {
  console.error('Error no manejado:', error);
  res.status(500).json({
    error: 'Error interno del servidor',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Algo salió mal'
  });
});

// Iniciar servidor
app.listen(PORT, () => {
  console.log(`🚀 Servidor Stripe ejecutándose en puerto ${PORT}`);
  console.log(`📊 Ambiente: ${process.env.NODE_ENV || 'development'}`);
  console.log(`💳 Stripe configurado: ${process.env.STRIPE_SECRET_KEY ? '✅' : '❌'}`);
});

module.exports = app;
