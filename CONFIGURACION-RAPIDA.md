# 🚀 Configuración Rápida de Stripe

## ✅ **Paso 1: Instalar Dependencias**

**Ejecuta en tu terminal:**
```bash
cd /Users/<USER>/PROJECTS/React/Anuncio-Cursos
npm install @stripe/stripe-js @stripe/react-stripe-js react-router-dom
```

## 🔑 **Paso 2: Obtener Claves de Stripe**

### **2.1 Crear cuenta:**
1. Ve a **https://stripe.com**
2. Haz clic en **"Start now"**
3. Completa el registro

### **2.2 Obtener claves:**
1. Ve a **https://dashboard.stripe.com/apikeys**
2. Copia la **"Publishable key"** (pk_test_...)
3. Copia la **"Secret key"** (sk_test_...)

## ⚙️ **Paso 3: Configurar Variables**

**Edita el archivo `.env` y reemplaza:**
```env
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_TU_CLAVE_PUBLICA_AQUI
STRIPE_SECRET_KEY=sk_test_TU_CLAVE_SECRETA_AQUI
```

## 🏪 **Paso 4: Activar OXXO**

1. Ve a **https://dashboard.stripe.com/settings/payment_methods**
2. Busca **"OXXO"**
3. **Actívalo** para México

## 🚀 **Paso 5: Probar el Sistema**

### **Tarjetas de Prueba:**
- **Éxito**: `4242 4242 4242 4242`
- **Fallo**: `4000 0000 0000 0002`
- **Fecha**: Cualquier fecha futura
- **CVC**: Cualquier 3 dígitos

### **Flujo de Prueba:**
1. Haz clic en **"Reserva tu lugar ahora"**
2. Llena el formulario
3. Selecciona método de pago
4. Usa tarjeta de prueba
5. ¡Verifica que funcione!

## 🔧 **Paso 6: Configurar Backend (Opcional)**

**Para pagos reales, instala el servidor:**
```bash
cd server
npm install
npm start
```

## ✅ **¡Listo!**

Tu sistema de pagos está configurado. Ahora puedes:
- ✅ **Procesar pagos** con tarjeta
- ✅ **Generar códigos OXXO**
- ✅ **Manejar errores**
- ✅ **Confirmar pagos**

## 🆘 **¿Problemas?**

### **Error: "No publishable key"**
- Verifica que la clave esté en `.env`
- Reinicia el servidor de desarrollo

### **Error: "Stripe not loaded"**
- Verifica tu conexión a internet
- Revisa la clave pública en Stripe

### **Error: "Payment failed"**
- Usa tarjetas de prueba válidas
- Verifica que OXXO esté activado

## 📞 **Soporte**

- **Documentación**: https://stripe.com/docs
- **Dashboard**: https://dashboard.stripe.com
- **Pruebas**: https://stripe.com/docs/testing
