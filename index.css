@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700;800&family=Open+Sans:wght@400;500;600&display=swap');

:root {
  --primary-color: #2563eb; /* Azul - Transmite confianza, profesionalismo y tecnología */
  --secondary-color: #10b981; /* Verde - Representa crecimiento y éxito */
  --accent-color: #f59e0b; /* Naranja - Estimula acción y decisión */
  --dark-color: #1e293b; /* Azul oscuro - Transmite autoridad y seriedad */
  --light-color: #f8fafc; /* Blanco hueso - Mejora legibilidad y sensación de espacio */
  --text-color: #334155; /* Gris oscuro - Mejor legibilidad que negro puro */
  --success-color: #10b981; /* Verde - Confirmación */
  --error-color: #ef4444; /* Rojo - Alertas */
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Open Sans', sans-serif;
  color: var(--text-color);
  line-height: 1.6;
  background-color: var(--light-color);
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
  color: var(--dark-color);
}

h1 {
  font-size: 3rem;
  font-weight: 800;
}

h2 {
  font-size: 2.5rem;
}

h3 {
  font-size: 2rem;
}

p {
  margin-bottom: 1.5rem;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  font-size: 1rem;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
  box-shadow: 0 4px 6px rgba(37, 99, 235, 0.25);
}

.btn-primary:hover {
  background-color: #1d4ed8;
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(37, 99, 235, 0.3);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
  box-shadow: 0 4px 6px rgba(16, 185, 129, 0.25);
}

.btn-secondary:hover {
  background-color: #059669;
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(16, 185, 129, 0.3);
}

.btn-accent {
  background-color: var(--accent-color);
  color: white;
  box-shadow: 0 4px 6px rgba(245, 158, 11, 0.25);
}

.btn-accent:hover {
  background-color: #d97706;
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(245, 158, 11, 0.3);
}

.btn-large {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

.text-center {
  text-align: center;
}

.section {
  padding: 5rem 0;
}

.section-title {
  text-align: center;
  margin-bottom: 3rem;
}

.grid {
  display: grid;
  gap: 2rem;
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

.card {
  background-color: white;
  border-radius: 0.5rem;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.whatsapp-btn {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  background-color: #25D366;
  color: white;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 10px rgba(37, 211, 102, 0.4);
  z-index: 100;
  transition: all 0.3s ease;
}

.whatsapp-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 14px rgba(37, 211, 102, 0.5);
}

.highlight {
  color: var(--primary-color);
  font-weight: 600;
}

.testimonial {
  font-style: italic;
  position: relative;
  padding: 1.5rem;
  background-color: rgba(37, 99, 235, 0.05);
  border-left: 4px solid var(--primary-color);
  margin-bottom: 2rem;
}

.testimonial-author {
  font-weight: 600;
  font-style: normal;
  margin-top: 1rem;
  display: block;
}

/* Responsive styles */
@media (max-width: 992px) {
  .grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  h1 {
    font-size: 2.5rem;
  }
  
  h2 {
    font-size: 2rem;
  }
  
  h3 {
    font-size: 1.75rem;
  }
  
  .grid-2, .grid-3, .grid-4 {
    grid-template-columns: 1fr;
  }
  
  .section {
    padding: 3rem 0;
  }
}

@media (max-width: 576px) {
  h1 {
    font-size: 2rem;
  }
  
  h2 {
    font-size: 1.75rem;
  }
  
  h3 {
    font-size: 1.5rem;
  }
  
  .btn-large {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }
}
