{"version": 3, "sources": ["../../react-is/cjs/react-is.development.js", "../../react-is/index.js", "../../object-assign/index.js", "../../prop-types/lib/ReactPropTypesSecret.js", "../../prop-types/lib/has.js", "../../prop-types/checkPropTypes.js", "../../prop-types/factoryWithTypeCheckers.js", "../../prop-types/index.js", "../../@stripe/react-stripe-js/dist/react-stripe.esm.mjs"], "sourcesContent": ["/** @license React v16.13.1\n * react-is.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n'use strict';\n\n// The Symbol used to tag the ReactElement-like types. If there is no native Symbol\n// nor polyfill, then a plain number is used for performance.\nvar hasSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = hasSymbol ? Symbol.for('react.element') : 0xeac7;\nvar REACT_PORTAL_TYPE = hasSymbol ? Symbol.for('react.portal') : 0xeaca;\nvar REACT_FRAGMENT_TYPE = hasSymbol ? Symbol.for('react.fragment') : 0xeacb;\nvar REACT_STRICT_MODE_TYPE = hasSymbol ? Symbol.for('react.strict_mode') : 0xeacc;\nvar REACT_PROFILER_TYPE = hasSymbol ? Symbol.for('react.profiler') : 0xead2;\nvar REACT_PROVIDER_TYPE = hasSymbol ? Symbol.for('react.provider') : 0xeacd;\nvar REACT_CONTEXT_TYPE = hasSymbol ? Symbol.for('react.context') : 0xeace; // TODO: We don't use AsyncMode or ConcurrentMode anymore. They were temporary\n// (unstable) APIs that have been removed. Can we remove the symbols?\n\nvar REACT_ASYNC_MODE_TYPE = hasSymbol ? Symbol.for('react.async_mode') : 0xeacf;\nvar REACT_CONCURRENT_MODE_TYPE = hasSymbol ? Symbol.for('react.concurrent_mode') : 0xeacf;\nvar REACT_FORWARD_REF_TYPE = hasSymbol ? Symbol.for('react.forward_ref') : 0xead0;\nvar REACT_SUSPENSE_TYPE = hasSymbol ? Symbol.for('react.suspense') : 0xead1;\nvar REACT_SUSPENSE_LIST_TYPE = hasSymbol ? Symbol.for('react.suspense_list') : 0xead8;\nvar REACT_MEMO_TYPE = hasSymbol ? Symbol.for('react.memo') : 0xead3;\nvar REACT_LAZY_TYPE = hasSymbol ? Symbol.for('react.lazy') : 0xead4;\nvar REACT_BLOCK_TYPE = hasSymbol ? Symbol.for('react.block') : 0xead9;\nvar REACT_FUNDAMENTAL_TYPE = hasSymbol ? Symbol.for('react.fundamental') : 0xead5;\nvar REACT_RESPONDER_TYPE = hasSymbol ? Symbol.for('react.responder') : 0xead6;\nvar REACT_SCOPE_TYPE = hasSymbol ? Symbol.for('react.scope') : 0xead7;\n\nfunction isValidElementType(type) {\n  return typeof type === 'string' || typeof type === 'function' || // Note: its typeof might be other than 'symbol' or 'number' if it's a polyfill.\n  type === REACT_FRAGMENT_TYPE || type === REACT_CONCURRENT_MODE_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || typeof type === 'object' && type !== null && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_FUNDAMENTAL_TYPE || type.$$typeof === REACT_RESPONDER_TYPE || type.$$typeof === REACT_SCOPE_TYPE || type.$$typeof === REACT_BLOCK_TYPE);\n}\n\nfunction typeOf(object) {\n  if (typeof object === 'object' && object !== null) {\n    var $$typeof = object.$$typeof;\n\n    switch ($$typeof) {\n      case REACT_ELEMENT_TYPE:\n        var type = object.type;\n\n        switch (type) {\n          case REACT_ASYNC_MODE_TYPE:\n          case REACT_CONCURRENT_MODE_TYPE:\n          case REACT_FRAGMENT_TYPE:\n          case REACT_PROFILER_TYPE:\n          case REACT_STRICT_MODE_TYPE:\n          case REACT_SUSPENSE_TYPE:\n            return type;\n\n          default:\n            var $$typeofType = type && type.$$typeof;\n\n            switch ($$typeofType) {\n              case REACT_CONTEXT_TYPE:\n              case REACT_FORWARD_REF_TYPE:\n              case REACT_LAZY_TYPE:\n              case REACT_MEMO_TYPE:\n              case REACT_PROVIDER_TYPE:\n                return $$typeofType;\n\n              default:\n                return $$typeof;\n            }\n\n        }\n\n      case REACT_PORTAL_TYPE:\n        return $$typeof;\n    }\n  }\n\n  return undefined;\n} // AsyncMode is deprecated along with isAsyncMode\n\nvar AsyncMode = REACT_ASYNC_MODE_TYPE;\nvar ConcurrentMode = REACT_CONCURRENT_MODE_TYPE;\nvar ContextConsumer = REACT_CONTEXT_TYPE;\nvar ContextProvider = REACT_PROVIDER_TYPE;\nvar Element = REACT_ELEMENT_TYPE;\nvar ForwardRef = REACT_FORWARD_REF_TYPE;\nvar Fragment = REACT_FRAGMENT_TYPE;\nvar Lazy = REACT_LAZY_TYPE;\nvar Memo = REACT_MEMO_TYPE;\nvar Portal = REACT_PORTAL_TYPE;\nvar Profiler = REACT_PROFILER_TYPE;\nvar StrictMode = REACT_STRICT_MODE_TYPE;\nvar Suspense = REACT_SUSPENSE_TYPE;\nvar hasWarnedAboutDeprecatedIsAsyncMode = false; // AsyncMode should be deprecated\n\nfunction isAsyncMode(object) {\n  {\n    if (!hasWarnedAboutDeprecatedIsAsyncMode) {\n      hasWarnedAboutDeprecatedIsAsyncMode = true; // Using console['warn'] to evade Babel and ESLint\n\n      console['warn']('The ReactIs.isAsyncMode() alias has been deprecated, ' + 'and will be removed in React 17+. Update your code to use ' + 'ReactIs.isConcurrentMode() instead. It has the exact same API.');\n    }\n  }\n\n  return isConcurrentMode(object) || typeOf(object) === REACT_ASYNC_MODE_TYPE;\n}\nfunction isConcurrentMode(object) {\n  return typeOf(object) === REACT_CONCURRENT_MODE_TYPE;\n}\nfunction isContextConsumer(object) {\n  return typeOf(object) === REACT_CONTEXT_TYPE;\n}\nfunction isContextProvider(object) {\n  return typeOf(object) === REACT_PROVIDER_TYPE;\n}\nfunction isElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\nfunction isForwardRef(object) {\n  return typeOf(object) === REACT_FORWARD_REF_TYPE;\n}\nfunction isFragment(object) {\n  return typeOf(object) === REACT_FRAGMENT_TYPE;\n}\nfunction isLazy(object) {\n  return typeOf(object) === REACT_LAZY_TYPE;\n}\nfunction isMemo(object) {\n  return typeOf(object) === REACT_MEMO_TYPE;\n}\nfunction isPortal(object) {\n  return typeOf(object) === REACT_PORTAL_TYPE;\n}\nfunction isProfiler(object) {\n  return typeOf(object) === REACT_PROFILER_TYPE;\n}\nfunction isStrictMode(object) {\n  return typeOf(object) === REACT_STRICT_MODE_TYPE;\n}\nfunction isSuspense(object) {\n  return typeOf(object) === REACT_SUSPENSE_TYPE;\n}\n\nexports.AsyncMode = AsyncMode;\nexports.ConcurrentMode = ConcurrentMode;\nexports.ContextConsumer = ContextConsumer;\nexports.ContextProvider = ContextProvider;\nexports.Element = Element;\nexports.ForwardRef = ForwardRef;\nexports.Fragment = Fragment;\nexports.Lazy = Lazy;\nexports.Memo = Memo;\nexports.Portal = Portal;\nexports.Profiler = Profiler;\nexports.StrictMode = StrictMode;\nexports.Suspense = Suspense;\nexports.isAsyncMode = isAsyncMode;\nexports.isConcurrentMode = isConcurrentMode;\nexports.isContextConsumer = isContextConsumer;\nexports.isContextProvider = isContextProvider;\nexports.isElement = isElement;\nexports.isForwardRef = isForwardRef;\nexports.isFragment = isFragment;\nexports.isLazy = isLazy;\nexports.isMemo = isMemo;\nexports.isPortal = isPortal;\nexports.isProfiler = isProfiler;\nexports.isStrictMode = isStrictMode;\nexports.isSuspense = isSuspense;\nexports.isValidElementType = isValidElementType;\nexports.typeOf = typeOf;\n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-is.production.min.js');\n} else {\n  module.exports = require('./cjs/react-is.development.js');\n}\n", "/*\nobject-assign\n(c) <PERSON><PERSON> Sorhus\n@license MIT\n*/\n\n'use strict';\n/* eslint-disable no-unused-vars */\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar propIsEnumerable = Object.prototype.propertyIsEnumerable;\n\nfunction toObject(val) {\n\tif (val === null || val === undefined) {\n\t\tthrow new TypeError('Object.assign cannot be called with null or undefined');\n\t}\n\n\treturn Object(val);\n}\n\nfunction shouldUseNative() {\n\ttry {\n\t\tif (!Object.assign) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// Detect buggy property enumeration order in older V8 versions.\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=4118\n\t\tvar test1 = new String('abc');  // eslint-disable-line no-new-wrappers\n\t\ttest1[5] = 'de';\n\t\tif (Object.getOwnPropertyNames(test1)[0] === '5') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test2 = {};\n\t\tfor (var i = 0; i < 10; i++) {\n\t\t\ttest2['_' + String.fromCharCode(i)] = i;\n\t\t}\n\t\tvar order2 = Object.getOwnPropertyNames(test2).map(function (n) {\n\t\t\treturn test2[n];\n\t\t});\n\t\tif (order2.join('') !== '0123456789') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test3 = {};\n\t\t'abcdefghijklmnopqrst'.split('').forEach(function (letter) {\n\t\t\ttest3[letter] = letter;\n\t\t});\n\t\tif (Object.keys(Object.assign({}, test3)).join('') !==\n\t\t\t\t'abcdefghijklmnopqrst') {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t} catch (err) {\n\t\t// We don't expect any of the above to throw, but better to be safe.\n\t\treturn false;\n\t}\n}\n\nmodule.exports = shouldUseNative() ? Object.assign : function (target, source) {\n\tvar from;\n\tvar to = toObject(target);\n\tvar symbols;\n\n\tfor (var s = 1; s < arguments.length; s++) {\n\t\tfrom = Object(arguments[s]);\n\n\t\tfor (var key in from) {\n\t\t\tif (hasOwnProperty.call(from, key)) {\n\t\t\t\tto[key] = from[key];\n\t\t\t}\n\t\t}\n\n\t\tif (getOwnPropertySymbols) {\n\t\t\tsymbols = getOwnPropertySymbols(from);\n\t\t\tfor (var i = 0; i < symbols.length; i++) {\n\t\t\t\tif (propIsEnumerable.call(from, symbols[i])) {\n\t\t\t\t\tto[symbols[i]] = from[symbols[i]];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn to;\n};\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n\nmodule.exports = ReactPropTypesSecret;\n", "module.exports = Function.call.bind(Object.prototype.hasOwnProperty);\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar printWarning = function() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\n  var loggedTypeFailures = {};\n  var has = require('./lib/has');\n\n  printWarning = function(text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) { /**/ }\n  };\n}\n\n/**\n * Assert that the values match with the type specs.\n * Error messages are memorized and will only be shown once.\n *\n * @param {object} typeSpecs Map of name to a ReactPropType\n * @param {object} values Runtime values that need to be type-checked\n * @param {string} location e.g. \"prop\", \"context\", \"child context\"\n * @param {string} componentName Name of the component for error messages.\n * @param {?Function} getStack Returns the component stack.\n * @private\n */\nfunction checkPropTypes(typeSpecs, values, location, componentName, getStack) {\n  if (process.env.NODE_ENV !== 'production') {\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error;\n        // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            var err = Error(\n              (componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' +\n              'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' +\n              'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.'\n            );\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n          error = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, ReactPropTypesSecret);\n        } catch (ex) {\n          error = ex;\n        }\n        if (error && !(error instanceof Error)) {\n          printWarning(\n            (componentName || 'React class') + ': type specification of ' +\n            location + ' `' + typeSpecName + '` is invalid; the type checker ' +\n            'function must return `null` or an `Error` but returned a ' + typeof error + '. ' +\n            'You may have forgotten to pass an argument to the type checker ' +\n            'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' +\n            'shape all require an argument).'\n          );\n        }\n        if (error instanceof Error && !(error.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error.message] = true;\n\n          var stack = getStack ? getStack() : '';\n\n          printWarning(\n            'Failed ' + location + ' type: ' + error.message + (stack != null ? stack : '')\n          );\n        }\n      }\n    }\n  }\n}\n\n/**\n * Resets warning cache when testing.\n *\n * @private\n */\ncheckPropTypes.resetWarningCache = function() {\n  if (process.env.NODE_ENV !== 'production') {\n    loggedTypeFailures = {};\n  }\n}\n\nmodule.exports = checkPropTypes;\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactIs = require('react-is');\nvar assign = require('object-assign');\n\nvar ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\nvar has = require('./lib/has');\nvar checkPropTypes = require('./checkPropTypes');\n\nvar printWarning = function() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  printWarning = function(text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) {}\n  };\n}\n\nfunction emptyFunctionThatReturnsNull() {\n  return null;\n}\n\nmodule.exports = function(isValidElement, throwOnDirectAccess) {\n  /* global Symbol */\n  var ITERATOR_SYMBOL = typeof Symbol === 'function' && Symbol.iterator;\n  var FAUX_ITERATOR_SYMBOL = '@@iterator'; // Before Symbol spec.\n\n  /**\n   * Returns the iterator method function contained on the iterable object.\n   *\n   * Be sure to invoke the function with the iterable as context:\n   *\n   *     var iteratorFn = getIteratorFn(myIterable);\n   *     if (iteratorFn) {\n   *       var iterator = iteratorFn.call(myIterable);\n   *       ...\n   *     }\n   *\n   * @param {?object} maybeIterable\n   * @return {?function}\n   */\n  function getIteratorFn(maybeIterable) {\n    var iteratorFn = maybeIterable && (ITERATOR_SYMBOL && maybeIterable[ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL]);\n    if (typeof iteratorFn === 'function') {\n      return iteratorFn;\n    }\n  }\n\n  /**\n   * Collection of methods that allow declaration and validation of props that are\n   * supplied to React components. Example usage:\n   *\n   *   var Props = require('ReactPropTypes');\n   *   var MyArticle = React.createClass({\n   *     propTypes: {\n   *       // An optional string prop named \"description\".\n   *       description: Props.string,\n   *\n   *       // A required enum prop named \"category\".\n   *       category: Props.oneOf(['News','Photos']).isRequired,\n   *\n   *       // A prop named \"dialog\" that requires an instance of Dialog.\n   *       dialog: Props.instanceOf(Dialog).isRequired\n   *     },\n   *     render: function() { ... }\n   *   });\n   *\n   * A more formal specification of how these methods are used:\n   *\n   *   type := array|bool|func|object|number|string|oneOf([...])|instanceOf(...)\n   *   decl := ReactPropTypes.{type}(.isRequired)?\n   *\n   * Each and every declaration produces a function with the same signature. This\n   * allows the creation of custom validation functions. For example:\n   *\n   *  var MyLink = React.createClass({\n   *    propTypes: {\n   *      // An optional string or URI prop named \"href\".\n   *      href: function(props, propName, componentName) {\n   *        var propValue = props[propName];\n   *        if (propValue != null && typeof propValue !== 'string' &&\n   *            !(propValue instanceof URI)) {\n   *          return new Error(\n   *            'Expected a string or an URI for ' + propName + ' in ' +\n   *            componentName\n   *          );\n   *        }\n   *      }\n   *    },\n   *    render: function() {...}\n   *  });\n   *\n   * @internal\n   */\n\n  var ANONYMOUS = '<<anonymous>>';\n\n  // Important!\n  // Keep this list in sync with production version in `./factoryWithThrowingShims.js`.\n  var ReactPropTypes = {\n    array: createPrimitiveTypeChecker('array'),\n    bigint: createPrimitiveTypeChecker('bigint'),\n    bool: createPrimitiveTypeChecker('boolean'),\n    func: createPrimitiveTypeChecker('function'),\n    number: createPrimitiveTypeChecker('number'),\n    object: createPrimitiveTypeChecker('object'),\n    string: createPrimitiveTypeChecker('string'),\n    symbol: createPrimitiveTypeChecker('symbol'),\n\n    any: createAnyTypeChecker(),\n    arrayOf: createArrayOfTypeChecker,\n    element: createElementTypeChecker(),\n    elementType: createElementTypeTypeChecker(),\n    instanceOf: createInstanceTypeChecker,\n    node: createNodeChecker(),\n    objectOf: createObjectOfTypeChecker,\n    oneOf: createEnumTypeChecker,\n    oneOfType: createUnionTypeChecker,\n    shape: createShapeTypeChecker,\n    exact: createStrictShapeTypeChecker,\n  };\n\n  /**\n   * inlined Object.is polyfill to avoid requiring consumers ship their own\n   * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n   */\n  /*eslint-disable no-self-compare*/\n  function is(x, y) {\n    // SameValue algorithm\n    if (x === y) {\n      // Steps 1-5, 7-10\n      // Steps 6.b-6.e: +0 != -0\n      return x !== 0 || 1 / x === 1 / y;\n    } else {\n      // Step 6.a: NaN == NaN\n      return x !== x && y !== y;\n    }\n  }\n  /*eslint-enable no-self-compare*/\n\n  /**\n   * We use an Error-like object for backward compatibility as people may call\n   * PropTypes directly and inspect their output. However, we don't use real\n   * Errors anymore. We don't inspect their stack anyway, and creating them\n   * is prohibitively expensive if they are created too often, such as what\n   * happens in oneOfType() for any type before the one that matched.\n   */\n  function PropTypeError(message, data) {\n    this.message = message;\n    this.data = data && typeof data === 'object' ? data: {};\n    this.stack = '';\n  }\n  // Make `instanceof Error` still work for returned errors.\n  PropTypeError.prototype = Error.prototype;\n\n  function createChainableTypeChecker(validate) {\n    if (process.env.NODE_ENV !== 'production') {\n      var manualPropTypeCallCache = {};\n      var manualPropTypeWarningCount = 0;\n    }\n    function checkType(isRequired, props, propName, componentName, location, propFullName, secret) {\n      componentName = componentName || ANONYMOUS;\n      propFullName = propFullName || propName;\n\n      if (secret !== ReactPropTypesSecret) {\n        if (throwOnDirectAccess) {\n          // New behavior only for users of `prop-types` package\n          var err = new Error(\n            'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n            'Use `PropTypes.checkPropTypes()` to call them. ' +\n            'Read more at http://fb.me/use-check-prop-types'\n          );\n          err.name = 'Invariant Violation';\n          throw err;\n        } else if (process.env.NODE_ENV !== 'production' && typeof console !== 'undefined') {\n          // Old behavior for people using React.PropTypes\n          var cacheKey = componentName + ':' + propName;\n          if (\n            !manualPropTypeCallCache[cacheKey] &&\n            // Avoid spamming the console because they are often not actionable except for lib authors\n            manualPropTypeWarningCount < 3\n          ) {\n            printWarning(\n              'You are manually calling a React.PropTypes validation ' +\n              'function for the `' + propFullName + '` prop on `' + componentName + '`. This is deprecated ' +\n              'and will throw in the standalone `prop-types` package. ' +\n              'You may be seeing this warning due to a third-party PropTypes ' +\n              'library. See https://fb.me/react-warning-dont-call-proptypes ' + 'for details.'\n            );\n            manualPropTypeCallCache[cacheKey] = true;\n            manualPropTypeWarningCount++;\n          }\n        }\n      }\n      if (props[propName] == null) {\n        if (isRequired) {\n          if (props[propName] === null) {\n            return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required ' + ('in `' + componentName + '`, but its value is `null`.'));\n          }\n          return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required in ' + ('`' + componentName + '`, but its value is `undefined`.'));\n        }\n        return null;\n      } else {\n        return validate(props, propName, componentName, location, propFullName);\n      }\n    }\n\n    var chainedCheckType = checkType.bind(null, false);\n    chainedCheckType.isRequired = checkType.bind(null, true);\n\n    return chainedCheckType;\n  }\n\n  function createPrimitiveTypeChecker(expectedType) {\n    function validate(props, propName, componentName, location, propFullName, secret) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== expectedType) {\n        // `propValue` being instance of, say, date/regexp, pass the 'object'\n        // check, but we can offer a more precise error message here rather than\n        // 'of type `object`'.\n        var preciseType = getPreciseType(propValue);\n\n        return new PropTypeError(\n          'Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + preciseType + '` supplied to `' + componentName + '`, expected ') + ('`' + expectedType + '`.'),\n          {expectedType: expectedType}\n        );\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createAnyTypeChecker() {\n    return createChainableTypeChecker(emptyFunctionThatReturnsNull);\n  }\n\n  function createArrayOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside arrayOf.');\n      }\n      var propValue = props[propName];\n      if (!Array.isArray(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an array.'));\n      }\n      for (var i = 0; i < propValue.length; i++) {\n        var error = typeChecker(propValue, i, componentName, location, propFullName + '[' + i + ']', ReactPropTypesSecret);\n        if (error instanceof Error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!isValidElement(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!ReactIs.isValidElementType(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement type.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createInstanceTypeChecker(expectedClass) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!(props[propName] instanceof expectedClass)) {\n        var expectedClassName = expectedClass.name || ANONYMOUS;\n        var actualClassName = getClassName(props[propName]);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + actualClassName + '` supplied to `' + componentName + '`, expected ') + ('instance of `' + expectedClassName + '`.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createEnumTypeChecker(expectedValues) {\n    if (!Array.isArray(expectedValues)) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (arguments.length > 1) {\n          printWarning(\n            'Invalid arguments supplied to oneOf, expected an array, got ' + arguments.length + ' arguments. ' +\n            'A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z]).'\n          );\n        } else {\n          printWarning('Invalid argument supplied to oneOf, expected an array.');\n        }\n      }\n      return emptyFunctionThatReturnsNull;\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      for (var i = 0; i < expectedValues.length; i++) {\n        if (is(propValue, expectedValues[i])) {\n          return null;\n        }\n      }\n\n      var valuesString = JSON.stringify(expectedValues, function replacer(key, value) {\n        var type = getPreciseType(value);\n        if (type === 'symbol') {\n          return String(value);\n        }\n        return value;\n      });\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of value `' + String(propValue) + '` ' + ('supplied to `' + componentName + '`, expected one of ' + valuesString + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createObjectOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside objectOf.');\n      }\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an object.'));\n      }\n      for (var key in propValue) {\n        if (has(propValue, key)) {\n          var error = typeChecker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n          if (error instanceof Error) {\n            return error;\n          }\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createUnionTypeChecker(arrayOfTypeCheckers) {\n    if (!Array.isArray(arrayOfTypeCheckers)) {\n      process.env.NODE_ENV !== 'production' ? printWarning('Invalid argument supplied to oneOfType, expected an instance of array.') : void 0;\n      return emptyFunctionThatReturnsNull;\n    }\n\n    for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n      var checker = arrayOfTypeCheckers[i];\n      if (typeof checker !== 'function') {\n        printWarning(\n          'Invalid argument supplied to oneOfType. Expected an array of check functions, but ' +\n          'received ' + getPostfixForTypeWarning(checker) + ' at index ' + i + '.'\n        );\n        return emptyFunctionThatReturnsNull;\n      }\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var expectedTypes = [];\n      for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n        var checker = arrayOfTypeCheckers[i];\n        var checkerResult = checker(props, propName, componentName, location, propFullName, ReactPropTypesSecret);\n        if (checkerResult == null) {\n          return null;\n        }\n        if (checkerResult.data && has(checkerResult.data, 'expectedType')) {\n          expectedTypes.push(checkerResult.data.expectedType);\n        }\n      }\n      var expectedTypesMessage = (expectedTypes.length > 0) ? ', expected one of type [' + expectedTypes.join(', ') + ']': '';\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`' + expectedTypesMessage + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createNodeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!isNode(props[propName])) {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`, expected a ReactNode.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function invalidValidatorError(componentName, location, propFullName, key, type) {\n    return new PropTypeError(\n      (componentName || 'React class') + ': ' + location + ' type `' + propFullName + '.' + key + '` is invalid; ' +\n      'it must be a function, usually from the `prop-types` package, but received `' + type + '`.'\n    );\n  }\n\n  function createShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      for (var key in shapeTypes) {\n        var checker = shapeTypes[key];\n        if (typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createStrictShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      // We need to check all keys in case some are required but missing from props.\n      var allKeys = assign({}, props[propName], shapeTypes);\n      for (var key in allKeys) {\n        var checker = shapeTypes[key];\n        if (has(shapeTypes, key) && typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n        if (!checker) {\n          return new PropTypeError(\n            'Invalid ' + location + ' `' + propFullName + '` key `' + key + '` supplied to `' + componentName + '`.' +\n            '\\nBad object: ' + JSON.stringify(props[propName], null, '  ') +\n            '\\nValid keys: ' + JSON.stringify(Object.keys(shapeTypes), null, '  ')\n          );\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n\n    return createChainableTypeChecker(validate);\n  }\n\n  function isNode(propValue) {\n    switch (typeof propValue) {\n      case 'number':\n      case 'string':\n      case 'undefined':\n        return true;\n      case 'boolean':\n        return !propValue;\n      case 'object':\n        if (Array.isArray(propValue)) {\n          return propValue.every(isNode);\n        }\n        if (propValue === null || isValidElement(propValue)) {\n          return true;\n        }\n\n        var iteratorFn = getIteratorFn(propValue);\n        if (iteratorFn) {\n          var iterator = iteratorFn.call(propValue);\n          var step;\n          if (iteratorFn !== propValue.entries) {\n            while (!(step = iterator.next()).done) {\n              if (!isNode(step.value)) {\n                return false;\n              }\n            }\n          } else {\n            // Iterator will provide entry [k,v] tuples rather than values.\n            while (!(step = iterator.next()).done) {\n              var entry = step.value;\n              if (entry) {\n                if (!isNode(entry[1])) {\n                  return false;\n                }\n              }\n            }\n          }\n        } else {\n          return false;\n        }\n\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  function isSymbol(propType, propValue) {\n    // Native Symbol.\n    if (propType === 'symbol') {\n      return true;\n    }\n\n    // falsy value can't be a Symbol\n    if (!propValue) {\n      return false;\n    }\n\n    // 19.4.3.5 Symbol.prototype[@@toStringTag] === 'Symbol'\n    if (propValue['@@toStringTag'] === 'Symbol') {\n      return true;\n    }\n\n    // Fallback for non-spec compliant Symbols which are polyfilled.\n    if (typeof Symbol === 'function' && propValue instanceof Symbol) {\n      return true;\n    }\n\n    return false;\n  }\n\n  // Equivalent of `typeof` but with special handling for array and regexp.\n  function getPropType(propValue) {\n    var propType = typeof propValue;\n    if (Array.isArray(propValue)) {\n      return 'array';\n    }\n    if (propValue instanceof RegExp) {\n      // Old webkits (at least until Android 4.0) return 'function' rather than\n      // 'object' for typeof a RegExp. We'll normalize this here so that /bla/\n      // passes PropTypes.object.\n      return 'object';\n    }\n    if (isSymbol(propType, propValue)) {\n      return 'symbol';\n    }\n    return propType;\n  }\n\n  // This handles more types than `getPropType`. Only used for error messages.\n  // See `createPrimitiveTypeChecker`.\n  function getPreciseType(propValue) {\n    if (typeof propValue === 'undefined' || propValue === null) {\n      return '' + propValue;\n    }\n    var propType = getPropType(propValue);\n    if (propType === 'object') {\n      if (propValue instanceof Date) {\n        return 'date';\n      } else if (propValue instanceof RegExp) {\n        return 'regexp';\n      }\n    }\n    return propType;\n  }\n\n  // Returns a string that is postfixed to a warning about an invalid type.\n  // For example, \"undefined\" or \"of type array\"\n  function getPostfixForTypeWarning(value) {\n    var type = getPreciseType(value);\n    switch (type) {\n      case 'array':\n      case 'object':\n        return 'an ' + type;\n      case 'boolean':\n      case 'date':\n      case 'regexp':\n        return 'a ' + type;\n      default:\n        return type;\n    }\n  }\n\n  // Returns class name of the object, if any.\n  function getClassName(propValue) {\n    if (!propValue.constructor || !propValue.constructor.name) {\n      return ANONYMOUS;\n    }\n    return propValue.constructor.name;\n  }\n\n  ReactPropTypes.checkPropTypes = checkPropTypes;\n  ReactPropTypes.resetWarningCache = checkPropTypes.resetWarningCache;\n  ReactPropTypes.PropTypes = ReactPropTypes;\n\n  return ReactPropTypes;\n};\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactIs = require('react-is');\n\n  // By explicitly using `prop-types` you are opting into new development behavior.\n  // http://fb.me/prop-types-in-prod\n  var throwOnDirectAccess = true;\n  module.exports = require('./factoryWithTypeCheckers')(ReactIs.isElement, throwOnDirectAccess);\n} else {\n  // By explicitly using `prop-types` you are opting into new production behavior.\n  // http://fb.me/prop-types-in-prod\n  module.exports = require('./factoryWithThrowingShims')();\n}\n", "import React from 'react';\nimport PropTypes from 'prop-types';\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n\n  var key, i;\n\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}\n\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\n\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = arr && (typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]);\n\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n\n  var _s, _e;\n\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nvar useAttachEvent = function useAttachEvent(element, event, cb) {\n  var cbDefined = !!cb;\n  var cbRef = React.useRef(cb); // In many integrations the callback prop changes on each render.\n  // Using a ref saves us from calling element.on/.off every render.\n\n  React.useEffect(function () {\n    cbRef.current = cb;\n  }, [cb]);\n  React.useEffect(function () {\n    if (!cbDefined || !element) {\n      return function () {};\n    }\n\n    var decoratedCb = function decoratedCb() {\n      if (cbRef.current) {\n        cbRef.current.apply(cbRef, arguments);\n      }\n    };\n\n    element.on(event, decoratedCb);\n    return function () {\n      element.off(event, decoratedCb);\n    };\n  }, [cbDefined, event, element, cbRef]);\n};\n\nvar usePrevious = function usePrevious(value) {\n  var ref = React.useRef(value);\n  React.useEffect(function () {\n    ref.current = value;\n  }, [value]);\n  return ref.current;\n};\n\nvar isUnknownObject = function isUnknownObject(raw) {\n  return raw !== null && _typeof(raw) === 'object';\n};\nvar isPromise = function isPromise(raw) {\n  return isUnknownObject(raw) && typeof raw.then === 'function';\n}; // We are using types to enforce the `stripe` prop in this lib,\n// but in an untyped integration `stripe` could be anything, so we need\n// to do some sanity validation to prevent type errors.\n\nvar isStripe = function isStripe(raw) {\n  return isUnknownObject(raw) && typeof raw.elements === 'function' && typeof raw.createToken === 'function' && typeof raw.createPaymentMethod === 'function' && typeof raw.confirmCardPayment === 'function';\n};\n\nvar PLAIN_OBJECT_STR = '[object Object]';\nvar isEqual = function isEqual(left, right) {\n  if (!isUnknownObject(left) || !isUnknownObject(right)) {\n    return left === right;\n  }\n\n  var leftArray = Array.isArray(left);\n  var rightArray = Array.isArray(right);\n  if (leftArray !== rightArray) return false;\n  var leftPlainObject = Object.prototype.toString.call(left) === PLAIN_OBJECT_STR;\n  var rightPlainObject = Object.prototype.toString.call(right) === PLAIN_OBJECT_STR;\n  if (leftPlainObject !== rightPlainObject) return false; // not sure what sort of special object this is (regexp is one option), so\n  // fallback to reference check.\n\n  if (!leftPlainObject && !leftArray) return left === right;\n  var leftKeys = Object.keys(left);\n  var rightKeys = Object.keys(right);\n  if (leftKeys.length !== rightKeys.length) return false;\n  var keySet = {};\n\n  for (var i = 0; i < leftKeys.length; i += 1) {\n    keySet[leftKeys[i]] = true;\n  }\n\n  for (var _i = 0; _i < rightKeys.length; _i += 1) {\n    keySet[rightKeys[_i]] = true;\n  }\n\n  var allKeys = Object.keys(keySet);\n\n  if (allKeys.length !== leftKeys.length) {\n    return false;\n  }\n\n  var l = left;\n  var r = right;\n\n  var pred = function pred(key) {\n    return isEqual(l[key], r[key]);\n  };\n\n  return allKeys.every(pred);\n};\n\nvar extractAllowedOptionsUpdates = function extractAllowedOptionsUpdates(options, prevOptions, immutableKeys) {\n  if (!isUnknownObject(options)) {\n    return null;\n  }\n\n  return Object.keys(options).reduce(function (newOptions, key) {\n    var isUpdated = !isUnknownObject(prevOptions) || !isEqual(options[key], prevOptions[key]);\n\n    if (immutableKeys.includes(key)) {\n      if (isUpdated) {\n        console.warn(\"Unsupported prop change: options.\".concat(key, \" is not a mutable property.\"));\n      }\n\n      return newOptions;\n    }\n\n    if (!isUpdated) {\n      return newOptions;\n    }\n\n    return _objectSpread2(_objectSpread2({}, newOptions || {}), {}, _defineProperty({}, key, options[key]));\n  }, null);\n};\n\nvar INVALID_STRIPE_ERROR$2 = 'Invalid prop `stripe` supplied to `Elements`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.'; // We are using types to enforce the `stripe` prop in this lib, but in a real\n// integration `stripe` could be anything, so we need to do some sanity\n// validation to prevent type errors.\n\nvar validateStripe = function validateStripe(maybeStripe) {\n  var errorMsg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : INVALID_STRIPE_ERROR$2;\n\n  if (maybeStripe === null || isStripe(maybeStripe)) {\n    return maybeStripe;\n  }\n\n  throw new Error(errorMsg);\n};\n\nvar parseStripeProp = function parseStripeProp(raw) {\n  var errorMsg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : INVALID_STRIPE_ERROR$2;\n\n  if (isPromise(raw)) {\n    return {\n      tag: 'async',\n      stripePromise: Promise.resolve(raw).then(function (result) {\n        return validateStripe(result, errorMsg);\n      })\n    };\n  }\n\n  var stripe = validateStripe(raw, errorMsg);\n\n  if (stripe === null) {\n    return {\n      tag: 'empty'\n    };\n  }\n\n  return {\n    tag: 'sync',\n    stripe: stripe\n  };\n};\n\nvar registerWithStripeJs = function registerWithStripeJs(stripe) {\n  if (!stripe || !stripe._registerWrapper || !stripe.registerAppInfo) {\n    return;\n  }\n\n  stripe._registerWrapper({\n    name: 'react-stripe-js',\n    version: \"2.8.1\"\n  });\n\n  stripe.registerAppInfo({\n    name: 'react-stripe-js',\n    version: \"2.8.1\",\n    url: 'https://stripe.com/docs/stripe-js/react'\n  });\n};\n\nvar ElementsContext = /*#__PURE__*/React.createContext(null);\nElementsContext.displayName = 'ElementsContext';\nvar parseElementsContext = function parseElementsContext(ctx, useCase) {\n  if (!ctx) {\n    throw new Error(\"Could not find Elements context; You need to wrap the part of your app that \".concat(useCase, \" in an <Elements> provider.\"));\n  }\n\n  return ctx;\n};\n/**\n * The `Elements` provider allows you to use [Element components](https://stripe.com/docs/stripe-js/react#element-components) and access the [Stripe object](https://stripe.com/docs/js/initializing) in any nested component.\n * Render an `Elements` provider at the root of your React app so that it is available everywhere you need it.\n *\n * To use the `Elements` provider, call `loadStripe` from `@stripe/stripe-js` with your publishable key.\n * The `loadStripe` function will asynchronously load the Stripe.js script and initialize a `Stripe` object.\n * Pass the returned `Promise` to `Elements`.\n *\n * @docs https://stripe.com/docs/stripe-js/react#elements-provider\n */\n\nvar Elements = function Elements(_ref) {\n  var rawStripeProp = _ref.stripe,\n      options = _ref.options,\n      children = _ref.children;\n  var parsed = React.useMemo(function () {\n    return parseStripeProp(rawStripeProp);\n  }, [rawStripeProp]); // For a sync stripe instance, initialize into context\n\n  var _React$useState = React.useState(function () {\n    return {\n      stripe: parsed.tag === 'sync' ? parsed.stripe : null,\n      elements: parsed.tag === 'sync' ? parsed.stripe.elements(options) : null\n    };\n  }),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      ctx = _React$useState2[0],\n      setContext = _React$useState2[1];\n\n  React.useEffect(function () {\n    var isMounted = true;\n\n    var safeSetContext = function safeSetContext(stripe) {\n      setContext(function (ctx) {\n        // no-op if we already have a stripe instance (https://github.com/stripe/react-stripe-js/issues/296)\n        if (ctx.stripe) return ctx;\n        return {\n          stripe: stripe,\n          elements: stripe.elements(options)\n        };\n      });\n    }; // For an async stripePromise, store it in context once resolved\n\n\n    if (parsed.tag === 'async' && !ctx.stripe) {\n      parsed.stripePromise.then(function (stripe) {\n        if (stripe && isMounted) {\n          // Only update Elements context if the component is still mounted\n          // and stripe is not null. We allow stripe to be null to make\n          // handling SSR easier.\n          safeSetContext(stripe);\n        }\n      });\n    } else if (parsed.tag === 'sync' && !ctx.stripe) {\n      // Or, handle a sync stripe instance going from null -> populated\n      safeSetContext(parsed.stripe);\n    }\n\n    return function () {\n      isMounted = false;\n    };\n  }, [parsed, ctx, options]); // Warn on changes to stripe prop\n\n  var prevStripe = usePrevious(rawStripeProp);\n  React.useEffect(function () {\n    if (prevStripe !== null && prevStripe !== rawStripeProp) {\n      console.warn('Unsupported prop change on Elements: You cannot change the `stripe` prop after setting it.');\n    }\n  }, [prevStripe, rawStripeProp]); // Apply updates to elements when options prop has relevant changes\n\n  var prevOptions = usePrevious(options);\n  React.useEffect(function () {\n    if (!ctx.elements) {\n      return;\n    }\n\n    var updates = extractAllowedOptionsUpdates(options, prevOptions, ['clientSecret', 'fonts']);\n\n    if (updates) {\n      ctx.elements.update(updates);\n    }\n  }, [options, prevOptions, ctx.elements]); // Attach react-stripe-js version to stripe.js instance\n\n  React.useEffect(function () {\n    registerWithStripeJs(ctx.stripe);\n  }, [ctx.stripe]);\n  return /*#__PURE__*/React.createElement(ElementsContext.Provider, {\n    value: ctx\n  }, children);\n};\nElements.propTypes = {\n  stripe: PropTypes.any,\n  options: PropTypes.object\n};\nvar useElementsContextWithUseCase = function useElementsContextWithUseCase(useCaseMessage) {\n  var ctx = React.useContext(ElementsContext);\n  return parseElementsContext(ctx, useCaseMessage);\n};\n/**\n * @docs https://stripe.com/docs/stripe-js/react#useelements-hook\n */\n\nvar useElements = function useElements() {\n  var _useElementsContextWi = useElementsContextWithUseCase('calls useElements()'),\n      elements = _useElementsContextWi.elements;\n\n  return elements;\n};\n/**\n * @docs https://stripe.com/docs/stripe-js/react#elements-consumer\n */\n\nvar ElementsConsumer = function ElementsConsumer(_ref2) {\n  var children = _ref2.children;\n  var ctx = useElementsContextWithUseCase('mounts <ElementsConsumer>'); // Assert to satisfy the busted React.FC return type (it should be ReactNode)\n\n  return children(ctx);\n};\nElementsConsumer.propTypes = {\n  children: PropTypes.func.isRequired\n};\n\nvar _excluded = [\"on\", \"session\"];\nvar CustomCheckoutSdkContext = /*#__PURE__*/React.createContext(null);\nCustomCheckoutSdkContext.displayName = 'CustomCheckoutSdkContext';\nvar parseCustomCheckoutSdkContext = function parseCustomCheckoutSdkContext(ctx, useCase) {\n  if (!ctx) {\n    throw new Error(\"Could not find CustomCheckoutProvider context; You need to wrap the part of your app that \".concat(useCase, \" in an <CustomCheckoutProvider> provider.\"));\n  }\n\n  return ctx;\n};\nvar CustomCheckoutContext = /*#__PURE__*/React.createContext(null);\nCustomCheckoutContext.displayName = 'CustomCheckoutContext';\nvar extractCustomCheckoutContextValue = function extractCustomCheckoutContextValue(customCheckoutSdk, sessionState) {\n  if (!customCheckoutSdk) {\n    return null;\n  }\n\n  customCheckoutSdk.on;\n      customCheckoutSdk.session;\n      var actions = _objectWithoutProperties(customCheckoutSdk, _excluded);\n\n  if (!sessionState) {\n    return _objectSpread2(_objectSpread2({}, actions), customCheckoutSdk.session());\n  }\n\n  return _objectSpread2(_objectSpread2({}, actions), sessionState);\n};\nvar INVALID_STRIPE_ERROR$1 = 'Invalid prop `stripe` supplied to `CustomCheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.';\nvar CustomCheckoutProvider = function CustomCheckoutProvider(_ref) {\n  var rawStripeProp = _ref.stripe,\n      options = _ref.options,\n      children = _ref.children;\n  var parsed = React.useMemo(function () {\n    return parseStripeProp(rawStripeProp, INVALID_STRIPE_ERROR$1);\n  }, [rawStripeProp]); // State used to trigger a re-render when sdk.session is updated\n\n  var _React$useState = React.useState(null),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      session = _React$useState2[0],\n      setSession = _React$useState2[1];\n\n  var _React$useState3 = React.useState(function () {\n    return {\n      stripe: parsed.tag === 'sync' ? parsed.stripe : null,\n      customCheckoutSdk: null\n    };\n  }),\n      _React$useState4 = _slicedToArray(_React$useState3, 2),\n      ctx = _React$useState4[0],\n      setContext = _React$useState4[1];\n\n  var safeSetContext = function safeSetContext(stripe, customCheckoutSdk) {\n    setContext(function (ctx) {\n      if (ctx.stripe && ctx.customCheckoutSdk) {\n        return ctx;\n      }\n\n      return {\n        stripe: stripe,\n        customCheckoutSdk: customCheckoutSdk\n      };\n    });\n  }; // Ref used to avoid calling initCustomCheckout multiple times when options changes\n\n\n  var initCustomCheckoutCalledRef = React.useRef(false);\n  React.useEffect(function () {\n    var isMounted = true;\n\n    if (parsed.tag === 'async' && !ctx.stripe) {\n      parsed.stripePromise.then(function (stripe) {\n        if (stripe && isMounted && !initCustomCheckoutCalledRef.current) {\n          // Only update context if the component is still mounted\n          // and stripe is not null. We allow stripe to be null to make\n          // handling SSR easier.\n          initCustomCheckoutCalledRef.current = true;\n          stripe.initCustomCheckout(options).then(function (customCheckoutSdk) {\n            if (customCheckoutSdk) {\n              safeSetContext(stripe, customCheckoutSdk);\n              customCheckoutSdk.on('change', setSession);\n            }\n          });\n        }\n      });\n    } else if (parsed.tag === 'sync' && parsed.stripe && !initCustomCheckoutCalledRef.current) {\n      initCustomCheckoutCalledRef.current = true;\n      parsed.stripe.initCustomCheckout(options).then(function (customCheckoutSdk) {\n        if (customCheckoutSdk) {\n          safeSetContext(parsed.stripe, customCheckoutSdk);\n          customCheckoutSdk.on('change', setSession);\n        }\n      });\n    }\n\n    return function () {\n      isMounted = false;\n    };\n  }, [parsed, ctx, options, setSession]); // Warn on changes to stripe prop\n\n  var prevStripe = usePrevious(rawStripeProp);\n  React.useEffect(function () {\n    if (prevStripe !== null && prevStripe !== rawStripeProp) {\n      console.warn('Unsupported prop change on CustomCheckoutProvider: You cannot change the `stripe` prop after setting it.');\n    }\n  }, [prevStripe, rawStripeProp]); // Apply updates to elements when options prop has relevant changes\n\n  var prevOptions = usePrevious(options);\n  React.useEffect(function () {\n    var _prevOptions$elements, _options$elementsOpti;\n\n    if (!ctx.customCheckoutSdk) {\n      return;\n    }\n\n    if (options.clientSecret && !isUnknownObject(prevOptions) && !isEqual(options.clientSecret, prevOptions.clientSecret)) {\n      console.warn('Unsupported prop change: options.client_secret is not a mutable property.');\n    }\n\n    var previousAppearance = prevOptions === null || prevOptions === void 0 ? void 0 : (_prevOptions$elements = prevOptions.elementsOptions) === null || _prevOptions$elements === void 0 ? void 0 : _prevOptions$elements.appearance;\n    var currentAppearance = options === null || options === void 0 ? void 0 : (_options$elementsOpti = options.elementsOptions) === null || _options$elementsOpti === void 0 ? void 0 : _options$elementsOpti.appearance;\n\n    if (currentAppearance && !isEqual(currentAppearance, previousAppearance)) {\n      ctx.customCheckoutSdk.changeAppearance(currentAppearance);\n    }\n  }, [options, prevOptions, ctx.customCheckoutSdk]); // Attach react-stripe-js version to stripe.js instance\n\n  React.useEffect(function () {\n    registerWithStripeJs(ctx.stripe);\n  }, [ctx.stripe]);\n  var customCheckoutContextValue = React.useMemo(function () {\n    return extractCustomCheckoutContextValue(ctx.customCheckoutSdk, session);\n  }, [ctx.customCheckoutSdk, session]);\n\n  if (!ctx.customCheckoutSdk) {\n    return null;\n  }\n\n  return /*#__PURE__*/React.createElement(CustomCheckoutSdkContext.Provider, {\n    value: ctx\n  }, /*#__PURE__*/React.createElement(CustomCheckoutContext.Provider, {\n    value: customCheckoutContextValue\n  }, children));\n};\nCustomCheckoutProvider.propTypes = {\n  stripe: PropTypes.any,\n  options: PropTypes.shape({\n    clientSecret: PropTypes.string.isRequired,\n    elementsOptions: PropTypes.object\n  }).isRequired\n};\nvar useCustomCheckoutSdkContextWithUseCase = function useCustomCheckoutSdkContextWithUseCase(useCaseString) {\n  var ctx = React.useContext(CustomCheckoutSdkContext);\n  return parseCustomCheckoutSdkContext(ctx, useCaseString);\n};\nvar useElementsOrCustomCheckoutSdkContextWithUseCase = function useElementsOrCustomCheckoutSdkContextWithUseCase(useCaseString) {\n  var customCheckoutSdkContext = React.useContext(CustomCheckoutSdkContext);\n  var elementsContext = React.useContext(ElementsContext);\n\n  if (customCheckoutSdkContext && elementsContext) {\n    throw new Error(\"You cannot wrap the part of your app that \".concat(useCaseString, \" in both <CustomCheckoutProvider> and <Elements> providers.\"));\n  }\n\n  if (customCheckoutSdkContext) {\n    return parseCustomCheckoutSdkContext(customCheckoutSdkContext, useCaseString);\n  }\n\n  return parseElementsContext(elementsContext, useCaseString);\n};\nvar useCustomCheckout = function useCustomCheckout() {\n  // ensure it's in CustomCheckoutProvider\n  useCustomCheckoutSdkContextWithUseCase('calls useCustomCheckout()');\n  var ctx = React.useContext(CustomCheckoutContext);\n\n  if (!ctx) {\n    throw new Error('Could not find CustomCheckout Context; You need to wrap the part of your app that calls useCustomCheckout() in an <CustomCheckoutProvider> provider.');\n  }\n\n  return ctx;\n};\n\nvar capitalized = function capitalized(str) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n};\n\nvar createElementComponent = function createElementComponent(type, isServer) {\n  var displayName = \"\".concat(capitalized(type), \"Element\");\n\n  var ClientElement = function ClientElement(_ref) {\n    var id = _ref.id,\n        className = _ref.className,\n        _ref$options = _ref.options,\n        options = _ref$options === void 0 ? {} : _ref$options,\n        onBlur = _ref.onBlur,\n        onFocus = _ref.onFocus,\n        onReady = _ref.onReady,\n        onChange = _ref.onChange,\n        onEscape = _ref.onEscape,\n        onClick = _ref.onClick,\n        onLoadError = _ref.onLoadError,\n        onLoaderStart = _ref.onLoaderStart,\n        onNetworksChange = _ref.onNetworksChange,\n        onConfirm = _ref.onConfirm,\n        onCancel = _ref.onCancel,\n        onShippingAddressChange = _ref.onShippingAddressChange,\n        onShippingRateChange = _ref.onShippingRateChange;\n    var ctx = useElementsOrCustomCheckoutSdkContextWithUseCase(\"mounts <\".concat(displayName, \">\"));\n    var elements = 'elements' in ctx ? ctx.elements : null;\n    var customCheckoutSdk = 'customCheckoutSdk' in ctx ? ctx.customCheckoutSdk : null;\n\n    var _React$useState = React.useState(null),\n        _React$useState2 = _slicedToArray(_React$useState, 2),\n        element = _React$useState2[0],\n        setElement = _React$useState2[1];\n\n    var elementRef = React.useRef(null);\n    var domNode = React.useRef(null); // For every event where the merchant provides a callback, call element.on\n    // with that callback. If the merchant ever changes the callback, removes\n    // the old callback with element.off and then call element.on with the new one.\n\n    useAttachEvent(element, 'blur', onBlur);\n    useAttachEvent(element, 'focus', onFocus);\n    useAttachEvent(element, 'escape', onEscape);\n    useAttachEvent(element, 'click', onClick);\n    useAttachEvent(element, 'loaderror', onLoadError);\n    useAttachEvent(element, 'loaderstart', onLoaderStart);\n    useAttachEvent(element, 'networkschange', onNetworksChange);\n    useAttachEvent(element, 'confirm', onConfirm);\n    useAttachEvent(element, 'cancel', onCancel);\n    useAttachEvent(element, 'shippingaddresschange', onShippingAddressChange);\n    useAttachEvent(element, 'shippingratechange', onShippingRateChange);\n    useAttachEvent(element, 'change', onChange);\n    var readyCallback;\n\n    if (onReady) {\n      if (type === 'expressCheckout') {\n        // Passes through the event, which includes visible PM types\n        readyCallback = onReady;\n      } else {\n        // For other Elements, pass through the Element itself.\n        readyCallback = function readyCallback() {\n          onReady(element);\n        };\n      }\n    }\n\n    useAttachEvent(element, 'ready', readyCallback);\n    React.useLayoutEffect(function () {\n      if (elementRef.current === null && domNode.current !== null && (elements || customCheckoutSdk)) {\n        var newElement = null;\n\n        if (customCheckoutSdk) {\n          newElement = customCheckoutSdk.createElement(type, options);\n        } else if (elements) {\n          newElement = elements.create(type, options);\n        } // Store element in a ref to ensure it's _immediately_ available in cleanup hooks in StrictMode\n\n\n        elementRef.current = newElement; // Store element in state to facilitate event listener attachment\n\n        setElement(newElement);\n\n        if (newElement) {\n          newElement.mount(domNode.current);\n        }\n      }\n    }, [elements, customCheckoutSdk, options]);\n    var prevOptions = usePrevious(options);\n    React.useEffect(function () {\n      if (!elementRef.current) {\n        return;\n      }\n\n      var updates = extractAllowedOptionsUpdates(options, prevOptions, ['paymentRequest']);\n\n      if (updates && 'update' in elementRef.current) {\n        elementRef.current.update(updates);\n      }\n    }, [options, prevOptions]);\n    React.useLayoutEffect(function () {\n      return function () {\n        if (elementRef.current && typeof elementRef.current.destroy === 'function') {\n          try {\n            elementRef.current.destroy();\n            elementRef.current = null;\n          } catch (error) {// Do nothing\n          }\n        }\n      };\n    }, []);\n    return /*#__PURE__*/React.createElement(\"div\", {\n      id: id,\n      className: className,\n      ref: domNode\n    });\n  }; // Only render the Element wrapper in a server environment.\n\n\n  var ServerElement = function ServerElement(props) {\n    useElementsOrCustomCheckoutSdkContextWithUseCase(\"mounts <\".concat(displayName, \">\"));\n    var id = props.id,\n        className = props.className;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      id: id,\n      className: className\n    });\n  };\n\n  var Element = isServer ? ServerElement : ClientElement;\n  Element.propTypes = {\n    id: PropTypes.string,\n    className: PropTypes.string,\n    onChange: PropTypes.func,\n    onBlur: PropTypes.func,\n    onFocus: PropTypes.func,\n    onReady: PropTypes.func,\n    onEscape: PropTypes.func,\n    onClick: PropTypes.func,\n    onLoadError: PropTypes.func,\n    onLoaderStart: PropTypes.func,\n    onNetworksChange: PropTypes.func,\n    onConfirm: PropTypes.func,\n    onCancel: PropTypes.func,\n    onShippingAddressChange: PropTypes.func,\n    onShippingRateChange: PropTypes.func,\n    options: PropTypes.object\n  };\n  Element.displayName = displayName;\n  Element.__elementType = type;\n  return Element;\n};\n\nvar isServer = typeof window === 'undefined';\n\nvar EmbeddedCheckoutContext = /*#__PURE__*/React.createContext(null);\nEmbeddedCheckoutContext.displayName = 'EmbeddedCheckoutProviderContext';\nvar useEmbeddedCheckoutContext = function useEmbeddedCheckoutContext() {\n  var ctx = React.useContext(EmbeddedCheckoutContext);\n\n  if (!ctx) {\n    throw new Error('<EmbeddedCheckout> must be used within <EmbeddedCheckoutProvider>');\n  }\n\n  return ctx;\n};\nvar INVALID_STRIPE_ERROR = 'Invalid prop `stripe` supplied to `EmbeddedCheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.';\nvar EmbeddedCheckoutProvider = function EmbeddedCheckoutProvider(_ref) {\n  var rawStripeProp = _ref.stripe,\n      options = _ref.options,\n      children = _ref.children;\n  var parsed = React.useMemo(function () {\n    return parseStripeProp(rawStripeProp, INVALID_STRIPE_ERROR);\n  }, [rawStripeProp]);\n  var embeddedCheckoutPromise = React.useRef(null);\n  var loadedStripe = React.useRef(null);\n\n  var _React$useState = React.useState({\n    embeddedCheckout: null\n  }),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      ctx = _React$useState2[0],\n      setContext = _React$useState2[1];\n\n  React.useEffect(function () {\n    // Don't support any ctx updates once embeddedCheckout or stripe is set.\n    if (loadedStripe.current || embeddedCheckoutPromise.current) {\n      return;\n    }\n\n    var setStripeAndInitEmbeddedCheckout = function setStripeAndInitEmbeddedCheckout(stripe) {\n      if (loadedStripe.current || embeddedCheckoutPromise.current) return;\n      loadedStripe.current = stripe;\n      embeddedCheckoutPromise.current = loadedStripe.current.initEmbeddedCheckout(options).then(function (embeddedCheckout) {\n        setContext({\n          embeddedCheckout: embeddedCheckout\n        });\n      });\n    }; // For an async stripePromise, store it once resolved\n\n\n    if (parsed.tag === 'async' && !loadedStripe.current && (options.clientSecret || options.fetchClientSecret)) {\n      parsed.stripePromise.then(function (stripe) {\n        if (stripe) {\n          setStripeAndInitEmbeddedCheckout(stripe);\n        }\n      });\n    } else if (parsed.tag === 'sync' && !loadedStripe.current && (options.clientSecret || options.fetchClientSecret)) {\n      // Or, handle a sync stripe instance going from null -> populated\n      setStripeAndInitEmbeddedCheckout(parsed.stripe);\n    }\n  }, [parsed, options, ctx, loadedStripe]);\n  React.useEffect(function () {\n    // cleanup on unmount\n    return function () {\n      // If embedded checkout is fully initialized, destroy it.\n      if (ctx.embeddedCheckout) {\n        embeddedCheckoutPromise.current = null;\n        ctx.embeddedCheckout.destroy();\n      } else if (embeddedCheckoutPromise.current) {\n        // If embedded checkout is still initializing, destroy it once\n        // it's done. This could be caused by unmounting very quickly\n        // after mounting.\n        embeddedCheckoutPromise.current.then(function () {\n          embeddedCheckoutPromise.current = null;\n\n          if (ctx.embeddedCheckout) {\n            ctx.embeddedCheckout.destroy();\n          }\n        });\n      }\n    };\n  }, [ctx.embeddedCheckout]); // Attach react-stripe-js version to stripe.js instance\n\n  React.useEffect(function () {\n    registerWithStripeJs(loadedStripe);\n  }, [loadedStripe]); // Warn on changes to stripe prop.\n  // The stripe prop value can only go from null to non-null once and\n  // can't be changed after that.\n\n  var prevStripe = usePrevious(rawStripeProp);\n  React.useEffect(function () {\n    if (prevStripe !== null && prevStripe !== rawStripeProp) {\n      console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the `stripe` prop after setting it.');\n    }\n  }, [prevStripe, rawStripeProp]); // Warn on changes to options.\n\n  var prevOptions = usePrevious(options);\n  React.useEffect(function () {\n    if (prevOptions == null) {\n      return;\n    }\n\n    if (options == null) {\n      console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot unset options after setting them.');\n      return;\n    }\n\n    if (options.clientSecret === undefined && options.fetchClientSecret === undefined) {\n      console.warn('Invalid props passed to EmbeddedCheckoutProvider: You must provide one of either `options.fetchClientSecret` or `options.clientSecret`.');\n    }\n\n    if (prevOptions.clientSecret != null && options.clientSecret !== prevOptions.clientSecret) {\n      console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the client secret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead.');\n    }\n\n    if (prevOptions.fetchClientSecret != null && options.fetchClientSecret !== prevOptions.fetchClientSecret) {\n      console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change fetchClientSecret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead.');\n    }\n\n    if (prevOptions.onComplete != null && options.onComplete !== prevOptions.onComplete) {\n      console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onComplete option after setting it.');\n    }\n\n    if (prevOptions.onShippingDetailsChange != null && options.onShippingDetailsChange !== prevOptions.onShippingDetailsChange) {\n      console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onShippingDetailsChange option after setting it.');\n    }\n\n    if (prevOptions.onLineItemsChange != null && options.onLineItemsChange !== prevOptions.onLineItemsChange) {\n      console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onLineItemsChange option after setting it.');\n    }\n  }, [prevOptions, options]);\n  return /*#__PURE__*/React.createElement(EmbeddedCheckoutContext.Provider, {\n    value: ctx\n  }, children);\n};\n\nvar EmbeddedCheckoutClientElement = function EmbeddedCheckoutClientElement(_ref) {\n  var id = _ref.id,\n      className = _ref.className;\n\n  var _useEmbeddedCheckoutC = useEmbeddedCheckoutContext(),\n      embeddedCheckout = _useEmbeddedCheckoutC.embeddedCheckout;\n\n  var isMounted = React.useRef(false);\n  var domNode = React.useRef(null);\n  React.useLayoutEffect(function () {\n    if (!isMounted.current && embeddedCheckout && domNode.current !== null) {\n      embeddedCheckout.mount(domNode.current);\n      isMounted.current = true;\n    } // Clean up on unmount\n\n\n    return function () {\n      if (isMounted.current && embeddedCheckout) {\n        try {\n          embeddedCheckout.unmount();\n          isMounted.current = false;\n        } catch (e) {// Do nothing.\n          // Parent effects are destroyed before child effects, so\n          // in cases where both the EmbeddedCheckoutProvider and\n          // the EmbeddedCheckout component are removed at the same\n          // time, the embeddedCheckout instance will be destroyed,\n          // which causes an error when calling unmount.\n        }\n      }\n    };\n  }, [embeddedCheckout]);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: domNode,\n    id: id,\n    className: className\n  });\n}; // Only render the wrapper in a server environment.\n\n\nvar EmbeddedCheckoutServerElement = function EmbeddedCheckoutServerElement(_ref2) {\n  var id = _ref2.id,\n      className = _ref2.className;\n  // Validate that we are in the right context by calling useEmbeddedCheckoutContext.\n  useEmbeddedCheckoutContext();\n  return /*#__PURE__*/React.createElement(\"div\", {\n    id: id,\n    className: className\n  });\n};\n\nvar EmbeddedCheckout = isServer ? EmbeddedCheckoutServerElement : EmbeddedCheckoutClientElement;\n\n/**\n * @docs https://stripe.com/docs/stripe-js/react#usestripe-hook\n */\n\nvar useStripe = function useStripe() {\n  var _useElementsOrCustomC = useElementsOrCustomCheckoutSdkContextWithUseCase('calls useStripe()'),\n      stripe = _useElementsOrCustomC.stripe;\n\n  return stripe;\n};\n\n/**\n * Requires beta access:\n * Contact [Stripe support](https://support.stripe.com/) for more information.\n *\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar AuBankAccountElement = createElementComponent('auBankAccount', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar CardElement = createElementComponent('card', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar CardNumberElement = createElementComponent('cardNumber', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar CardExpiryElement = createElementComponent('cardExpiry', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar CardCvcElement = createElementComponent('cardCvc', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar FpxBankElement = createElementComponent('fpxBank', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar IbanElement = createElementComponent('iban', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar IdealBankElement = createElementComponent('idealBank', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar P24BankElement = createElementComponent('p24Bank', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar EpsBankElement = createElementComponent('epsBank', isServer);\nvar PaymentElement = createElementComponent('payment', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar ExpressCheckoutElement = createElementComponent('expressCheckout', isServer);\n/**\n * Requires beta access:\n * Contact [Stripe support](https://support.stripe.com/) for more information.\n */\n\nvar CurrencySelectorElement = createElementComponent('currencySelector', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar PaymentRequestButtonElement = createElementComponent('paymentRequestButton', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar LinkAuthenticationElement = createElementComponent('linkAuthentication', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar AddressElement = createElementComponent('address', isServer);\n/**\n * @deprecated\n * Use `AddressElement` instead.\n *\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar ShippingAddressElement = createElementComponent('shippingAddress', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar PaymentMethodMessagingElement = createElementComponent('paymentMethodMessaging', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar AffirmMessageElement = createElementComponent('affirmMessage', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar AfterpayClearpayMessageElement = createElementComponent('afterpayClearpayMessage', isServer);\n\nexport { AddressElement, AffirmMessageElement, AfterpayClearpayMessageElement, AuBankAccountElement, CardCvcElement, CardElement, CardExpiryElement, CardNumberElement, CurrencySelectorElement, CustomCheckoutProvider, Elements, ElementsConsumer, EmbeddedCheckout, EmbeddedCheckoutProvider, EpsBankElement, ExpressCheckoutElement, FpxBankElement, IbanElement, IdealBankElement, LinkAuthenticationElement, P24BankElement, PaymentElement, PaymentMethodMessagingElement, PaymentRequestButtonElement, ShippingAddressElement, useCustomCheckout, useElements, useStripe };\n"], "mappings": ";;;;;;;;;AAAA;AAAA;AAAA;AAaA,QAAI,MAAuC;AACzC,OAAC,WAAW;AACd;AAIA,YAAI,YAAY,OAAO,WAAW,cAAc,OAAO;AACvD,YAAI,qBAAqB,YAAY,OAAO,IAAI,eAAe,IAAI;AACnE,YAAI,oBAAoB,YAAY,OAAO,IAAI,cAAc,IAAI;AACjE,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,qBAAqB,YAAY,OAAO,IAAI,eAAe,IAAI;AAGnE,YAAI,wBAAwB,YAAY,OAAO,IAAI,kBAAkB,IAAI;AACzE,YAAI,6BAA6B,YAAY,OAAO,IAAI,uBAAuB,IAAI;AACnF,YAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,2BAA2B,YAAY,OAAO,IAAI,qBAAqB,IAAI;AAC/E,YAAI,kBAAkB,YAAY,OAAO,IAAI,YAAY,IAAI;AAC7D,YAAI,kBAAkB,YAAY,OAAO,IAAI,YAAY,IAAI;AAC7D,YAAI,mBAAmB,YAAY,OAAO,IAAI,aAAa,IAAI;AAC/D,YAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,YAAI,uBAAuB,YAAY,OAAO,IAAI,iBAAiB,IAAI;AACvE,YAAI,mBAAmB,YAAY,OAAO,IAAI,aAAa,IAAI;AAE/D,iBAAS,mBAAmB,MAAM;AAChC,iBAAO,OAAO,SAAS,YAAY,OAAO,SAAS;AAAA,UACnD,SAAS,uBAAuB,SAAS,8BAA8B,SAAS,uBAAuB,SAAS,0BAA0B,SAAS,uBAAuB,SAAS,4BAA4B,OAAO,SAAS,YAAY,SAAS,SAAS,KAAK,aAAa,mBAAmB,KAAK,aAAa,mBAAmB,KAAK,aAAa,uBAAuB,KAAK,aAAa,sBAAsB,KAAK,aAAa,0BAA0B,KAAK,aAAa,0BAA0B,KAAK,aAAa,wBAAwB,KAAK,aAAa,oBAAoB,KAAK,aAAa;AAAA,QACplB;AAEA,iBAAS,OAAO,QAAQ;AACtB,cAAI,OAAO,WAAW,YAAY,WAAW,MAAM;AACjD,gBAAI,WAAW,OAAO;AAEtB,oBAAQ,UAAU;AAAA,cAChB,KAAK;AACH,oBAAI,OAAO,OAAO;AAElB,wBAAQ,MAAM;AAAA,kBACZ,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AACH,2BAAO;AAAA,kBAET;AACE,wBAAI,eAAe,QAAQ,KAAK;AAEhC,4BAAQ,cAAc;AAAA,sBACpB,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AACH,+BAAO;AAAA,sBAET;AACE,+BAAO;AAAA,oBACX;AAAA,gBAEJ;AAAA,cAEF,KAAK;AACH,uBAAO;AAAA,YACX;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,YAAI,YAAY;AAChB,YAAI,iBAAiB;AACrB,YAAI,kBAAkB;AACtB,YAAI,kBAAkB;AACtB,YAAI,UAAU;AACd,YAAI,aAAa;AACjB,YAAI,WAAW;AACf,YAAI,OAAO;AACX,YAAI,OAAO;AACX,YAAI,SAAS;AACb,YAAI,WAAW;AACf,YAAI,aAAa;AACjB,YAAI,WAAW;AACf,YAAI,sCAAsC;AAE1C,iBAAS,YAAY,QAAQ;AAC3B;AACE,gBAAI,CAAC,qCAAqC;AACxC,oDAAsC;AAEtC,sBAAQ,MAAM,EAAE,+KAAyL;AAAA,YAC3M;AAAA,UACF;AAEA,iBAAO,iBAAiB,MAAM,KAAK,OAAO,MAAM,MAAM;AAAA,QACxD;AACA,iBAAS,iBAAiB,QAAQ;AAChC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,kBAAkB,QAAQ;AACjC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,kBAAkB,QAAQ;AACjC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,UAAU,QAAQ;AACzB,iBAAO,OAAO,WAAW,YAAY,WAAW,QAAQ,OAAO,aAAa;AAAA,QAC9E;AACA,iBAAS,aAAa,QAAQ;AAC5B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,OAAO,QAAQ;AACtB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,OAAO,QAAQ;AACtB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,SAAS,QAAQ;AACxB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,aAAa,QAAQ;AAC5B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AAEA,gBAAQ,YAAY;AACpB,gBAAQ,iBAAiB;AACzB,gBAAQ,kBAAkB;AAC1B,gBAAQ,kBAAkB;AAC1B,gBAAQ,UAAU;AAClB,gBAAQ,aAAa;AACrB,gBAAQ,WAAW;AACnB,gBAAQ,OAAO;AACf,gBAAQ,OAAO;AACf,gBAAQ,SAAS;AACjB,gBAAQ,WAAW;AACnB,gBAAQ,aAAa;AACrB,gBAAQ,WAAW;AACnB,gBAAQ,cAAc;AACtB,gBAAQ,mBAAmB;AAC3B,gBAAQ,oBAAoB;AAC5B,gBAAQ,oBAAoB;AAC5B,gBAAQ,YAAY;AACpB,gBAAQ,eAAe;AACvB,gBAAQ,aAAa;AACrB,gBAAQ,SAAS;AACjB,gBAAQ,SAAS;AACjB,gBAAQ,WAAW;AACnB,gBAAQ,aAAa;AACrB,gBAAQ,eAAe;AACvB,gBAAQ,aAAa;AACrB,gBAAQ,qBAAqB;AAC7B,gBAAQ,SAAS;AAAA,MACf,GAAG;AAAA,IACL;AAAA;AAAA;;;ACpLA;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACNA;AAAA;AAAA;AAQA,QAAI,wBAAwB,OAAO;AACnC,QAAI,iBAAiB,OAAO,UAAU;AACtC,QAAI,mBAAmB,OAAO,UAAU;AAExC,aAAS,SAAS,KAAK;AACtB,UAAI,QAAQ,QAAQ,QAAQ,QAAW;AACtC,cAAM,IAAI,UAAU,uDAAuD;AAAA,MAC5E;AAEA,aAAO,OAAO,GAAG;AAAA,IAClB;AAEA,aAAS,kBAAkB;AAC1B,UAAI;AACH,YAAI,CAAC,OAAO,QAAQ;AACnB,iBAAO;AAAA,QACR;AAKA,YAAI,QAAQ,IAAI,OAAO,KAAK;AAC5B,cAAM,CAAC,IAAI;AACX,YAAI,OAAO,oBAAoB,KAAK,EAAE,CAAC,MAAM,KAAK;AACjD,iBAAO;AAAA,QACR;AAGA,YAAI,QAAQ,CAAC;AACb,iBAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC5B,gBAAM,MAAM,OAAO,aAAa,CAAC,CAAC,IAAI;AAAA,QACvC;AACA,YAAI,SAAS,OAAO,oBAAoB,KAAK,EAAE,IAAI,SAAU,GAAG;AAC/D,iBAAO,MAAM,CAAC;AAAA,QACf,CAAC;AACD,YAAI,OAAO,KAAK,EAAE,MAAM,cAAc;AACrC,iBAAO;AAAA,QACR;AAGA,YAAI,QAAQ,CAAC;AACb,+BAAuB,MAAM,EAAE,EAAE,QAAQ,SAAU,QAAQ;AAC1D,gBAAM,MAAM,IAAI;AAAA,QACjB,CAAC;AACD,YAAI,OAAO,KAAK,OAAO,OAAO,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,EAAE,MAC/C,wBAAwB;AACzB,iBAAO;AAAA,QACR;AAEA,eAAO;AAAA,MACR,SAAS,KAAK;AAEb,eAAO;AAAA,MACR;AAAA,IACD;AAEA,WAAO,UAAU,gBAAgB,IAAI,OAAO,SAAS,SAAU,QAAQ,QAAQ;AAC9E,UAAI;AACJ,UAAI,KAAK,SAAS,MAAM;AACxB,UAAI;AAEJ,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC1C,eAAO,OAAO,UAAU,CAAC,CAAC;AAE1B,iBAAS,OAAO,MAAM;AACrB,cAAI,eAAe,KAAK,MAAM,GAAG,GAAG;AACnC,eAAG,GAAG,IAAI,KAAK,GAAG;AAAA,UACnB;AAAA,QACD;AAEA,YAAI,uBAAuB;AAC1B,oBAAU,sBAAsB,IAAI;AACpC,mBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACxC,gBAAI,iBAAiB,KAAK,MAAM,QAAQ,CAAC,CAAC,GAAG;AAC5C,iBAAG,QAAQ,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;AAAA,YACjC;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAAA;AAAA;;;ACzFA;AAAA;AAAA;AASA,QAAI,uBAAuB;AAE3B,WAAO,UAAU;AAAA;AAAA;;;ACXjB;AAAA;AAAA,WAAO,UAAU,SAAS,KAAK,KAAK,OAAO,UAAU,cAAc;AAAA;AAAA;;;ACAnE;AAAA;AAAA;AASA,QAAI,eAAe,WAAW;AAAA,IAAC;AAE/B,QAAI,MAAuC;AACrC,6BAAuB;AACvB,2BAAqB,CAAC;AACtB,YAAM;AAEV,qBAAe,SAAS,MAAM;AAC5B,YAAI,UAAU,cAAc;AAC5B,YAAI,OAAO,YAAY,aAAa;AAClC,kBAAQ,MAAM,OAAO;AAAA,QACvB;AACA,YAAI;AAIF,gBAAM,IAAI,MAAM,OAAO;AAAA,QACzB,SAAS,GAAG;AAAA,QAAO;AAAA,MACrB;AAAA,IACF;AAhBM;AACA;AACA;AA2BN,aAAS,eAAe,WAAW,QAAQ,UAAU,eAAe,UAAU;AAC5E,UAAI,MAAuC;AACzC,iBAAS,gBAAgB,WAAW;AAClC,cAAI,IAAI,WAAW,YAAY,GAAG;AAChC,gBAAI;AAIJ,gBAAI;AAGF,kBAAI,OAAO,UAAU,YAAY,MAAM,YAAY;AACjD,oBAAI,MAAM;AAAA,mBACP,iBAAiB,iBAAiB,OAAO,WAAW,YAAY,eAAe,+FACC,OAAO,UAAU,YAAY,IAAI;AAAA,gBAEpH;AACA,oBAAI,OAAO;AACX,sBAAM;AAAA,cACR;AACA,sBAAQ,UAAU,YAAY,EAAE,QAAQ,cAAc,eAAe,UAAU,MAAM,oBAAoB;AAAA,YAC3G,SAAS,IAAI;AACX,sBAAQ;AAAA,YACV;AACA,gBAAI,SAAS,EAAE,iBAAiB,QAAQ;AACtC;AAAA,iBACG,iBAAiB,iBAAiB,6BACnC,WAAW,OAAO,eAAe,6FAC6B,OAAO,QAAQ;AAAA,cAI/E;AAAA,YACF;AACA,gBAAI,iBAAiB,SAAS,EAAE,MAAM,WAAW,qBAAqB;AAGpE,iCAAmB,MAAM,OAAO,IAAI;AAEpC,kBAAI,QAAQ,WAAW,SAAS,IAAI;AAEpC;AAAA,gBACE,YAAY,WAAW,YAAY,MAAM,WAAW,SAAS,OAAO,QAAQ;AAAA,cAC9E;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAOA,mBAAe,oBAAoB,WAAW;AAC5C,UAAI,MAAuC;AACzC,6BAAqB,CAAC;AAAA,MACxB;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACtGjB;AAAA;AAAA;AASA,QAAI,UAAU;AACd,QAAI,SAAS;AAEb,QAAI,uBAAuB;AAC3B,QAAI,MAAM;AACV,QAAI,iBAAiB;AAErB,QAAI,eAAe,WAAW;AAAA,IAAC;AAE/B,QAAI,MAAuC;AACzC,qBAAe,SAAS,MAAM;AAC5B,YAAI,UAAU,cAAc;AAC5B,YAAI,OAAO,YAAY,aAAa;AAClC,kBAAQ,MAAM,OAAO;AAAA,QACvB;AACA,YAAI;AAIF,gBAAM,IAAI,MAAM,OAAO;AAAA,QACzB,SAAS,GAAG;AAAA,QAAC;AAAA,MACf;AAAA,IACF;AAEA,aAAS,+BAA+B;AACtC,aAAO;AAAA,IACT;AAEA,WAAO,UAAU,SAAS,gBAAgB,qBAAqB;AAE7D,UAAI,kBAAkB,OAAO,WAAW,cAAc,OAAO;AAC7D,UAAI,uBAAuB;AAgB3B,eAAS,cAAc,eAAe;AACpC,YAAI,aAAa,kBAAkB,mBAAmB,cAAc,eAAe,KAAK,cAAc,oBAAoB;AAC1H,YAAI,OAAO,eAAe,YAAY;AACpC,iBAAO;AAAA,QACT;AAAA,MACF;AAiDA,UAAI,YAAY;AAIhB,UAAI,iBAAiB;AAAA,QACnB,OAAO,2BAA2B,OAAO;AAAA,QACzC,QAAQ,2BAA2B,QAAQ;AAAA,QAC3C,MAAM,2BAA2B,SAAS;AAAA,QAC1C,MAAM,2BAA2B,UAAU;AAAA,QAC3C,QAAQ,2BAA2B,QAAQ;AAAA,QAC3C,QAAQ,2BAA2B,QAAQ;AAAA,QAC3C,QAAQ,2BAA2B,QAAQ;AAAA,QAC3C,QAAQ,2BAA2B,QAAQ;AAAA,QAE3C,KAAK,qBAAqB;AAAA,QAC1B,SAAS;AAAA,QACT,SAAS,yBAAyB;AAAA,QAClC,aAAa,6BAA6B;AAAA,QAC1C,YAAY;AAAA,QACZ,MAAM,kBAAkB;AAAA,QACxB,UAAU;AAAA,QACV,OAAO;AAAA,QACP,WAAW;AAAA,QACX,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAOA,eAAS,GAAG,GAAG,GAAG;AAEhB,YAAI,MAAM,GAAG;AAGX,iBAAO,MAAM,KAAK,IAAI,MAAM,IAAI;AAAA,QAClC,OAAO;AAEL,iBAAO,MAAM,KAAK,MAAM;AAAA,QAC1B;AAAA,MACF;AAUA,eAAS,cAAc,SAAS,MAAM;AACpC,aAAK,UAAU;AACf,aAAK,OAAO,QAAQ,OAAO,SAAS,WAAW,OAAM,CAAC;AACtD,aAAK,QAAQ;AAAA,MACf;AAEA,oBAAc,YAAY,MAAM;AAEhC,eAAS,2BAA2B,UAAU;AAC5C,YAAI,MAAuC;AACzC,cAAI,0BAA0B,CAAC;AAC/B,cAAI,6BAA6B;AAAA,QACnC;AACA,iBAAS,UAAU,YAAY,OAAO,UAAU,eAAe,UAAU,cAAc,QAAQ;AAC7F,0BAAgB,iBAAiB;AACjC,yBAAe,gBAAgB;AAE/B,cAAI,WAAW,sBAAsB;AACnC,gBAAI,qBAAqB;AAEvB,kBAAI,MAAM,IAAI;AAAA,gBACZ;AAAA,cAGF;AACA,kBAAI,OAAO;AACX,oBAAM;AAAA,YACR,WAAoD,OAAO,YAAY,aAAa;AAElF,kBAAI,WAAW,gBAAgB,MAAM;AACrC,kBACE,CAAC,wBAAwB,QAAQ;AAAA,cAEjC,6BAA6B,GAC7B;AACA;AAAA,kBACE,6EACuB,eAAe,gBAAgB,gBAAgB;AAAA,gBAIxE;AACA,wCAAwB,QAAQ,IAAI;AACpC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,cAAI,MAAM,QAAQ,KAAK,MAAM;AAC3B,gBAAI,YAAY;AACd,kBAAI,MAAM,QAAQ,MAAM,MAAM;AAC5B,uBAAO,IAAI,cAAc,SAAS,WAAW,OAAO,eAAe,8BAA8B,SAAS,gBAAgB,8BAA8B;AAAA,cAC1J;AACA,qBAAO,IAAI,cAAc,SAAS,WAAW,OAAO,eAAe,iCAAiC,MAAM,gBAAgB,mCAAmC;AAAA,YAC/J;AACA,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO,SAAS,OAAO,UAAU,eAAe,UAAU,YAAY;AAAA,UACxE;AAAA,QACF;AAEA,YAAI,mBAAmB,UAAU,KAAK,MAAM,KAAK;AACjD,yBAAiB,aAAa,UAAU,KAAK,MAAM,IAAI;AAEvD,eAAO;AAAA,MACT;AAEA,eAAS,2BAA2B,cAAc;AAChD,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc,QAAQ;AAChF,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,cAAI,aAAa,cAAc;AAI7B,gBAAI,cAAc,eAAe,SAAS;AAE1C,mBAAO,IAAI;AAAA,cACT,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,cAAc,oBAAoB,gBAAgB,mBAAmB,MAAM,eAAe;AAAA,cAC9J,EAAC,aAA0B;AAAA,YAC7B;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,uBAAuB;AAC9B,eAAO,2BAA2B,4BAA4B;AAAA,MAChE;AAEA,eAAS,yBAAyB,aAAa;AAC7C,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,OAAO,gBAAgB,YAAY;AACrC,mBAAO,IAAI,cAAc,eAAe,eAAe,qBAAqB,gBAAgB,iDAAiD;AAAA,UAC/I;AACA,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,CAAC,MAAM,QAAQ,SAAS,GAAG;AAC7B,gBAAI,WAAW,YAAY,SAAS;AACpC,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,wBAAwB;AAAA,UACtK;AACA,mBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,gBAAI,QAAQ,YAAY,WAAW,GAAG,eAAe,UAAU,eAAe,MAAM,IAAI,KAAK,oBAAoB;AACjH,gBAAI,iBAAiB,OAAO;AAC1B,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,2BAA2B;AAClC,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,CAAC,eAAe,SAAS,GAAG;AAC9B,gBAAI,WAAW,YAAY,SAAS;AACpC,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,qCAAqC;AAAA,UACnL;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,+BAA+B;AACtC,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,CAAC,QAAQ,mBAAmB,SAAS,GAAG;AAC1C,gBAAI,WAAW,YAAY,SAAS;AACpC,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,0CAA0C;AAAA,UACxL;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,0BAA0B,eAAe;AAChD,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,EAAE,MAAM,QAAQ,aAAa,gBAAgB;AAC/C,gBAAI,oBAAoB,cAAc,QAAQ;AAC9C,gBAAI,kBAAkB,aAAa,MAAM,QAAQ,CAAC;AAClD,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,kBAAkB,oBAAoB,gBAAgB,mBAAmB,kBAAkB,oBAAoB,KAAK;AAAA,UACnN;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,sBAAsB,gBAAgB;AAC7C,YAAI,CAAC,MAAM,QAAQ,cAAc,GAAG;AAClC,cAAI,MAAuC;AACzC,gBAAI,UAAU,SAAS,GAAG;AACxB;AAAA,gBACE,iEAAiE,UAAU,SAAS;AAAA,cAEtF;AAAA,YACF,OAAO;AACL,2BAAa,wDAAwD;AAAA,YACvE;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAEA,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,mBAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,gBAAI,GAAG,WAAW,eAAe,CAAC,CAAC,GAAG;AACpC,qBAAO;AAAA,YACT;AAAA,UACF;AAEA,cAAI,eAAe,KAAK,UAAU,gBAAgB,SAAS,SAAS,KAAK,OAAO;AAC9E,gBAAI,OAAO,eAAe,KAAK;AAC/B,gBAAI,SAAS,UAAU;AACrB,qBAAO,OAAO,KAAK;AAAA,YACrB;AACA,mBAAO;AAAA,UACT,CAAC;AACD,iBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,iBAAiB,OAAO,SAAS,IAAI,QAAQ,kBAAkB,gBAAgB,wBAAwB,eAAe,IAAI;AAAA,QACnM;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,0BAA0B,aAAa;AAC9C,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,OAAO,gBAAgB,YAAY;AACrC,mBAAO,IAAI,cAAc,eAAe,eAAe,qBAAqB,gBAAgB,kDAAkD;AAAA,UAChJ;AACA,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,cAAI,aAAa,UAAU;AACzB,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,yBAAyB;AAAA,UACvK;AACA,mBAAS,OAAO,WAAW;AACzB,gBAAI,IAAI,WAAW,GAAG,GAAG;AACvB,kBAAI,QAAQ,YAAY,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK,oBAAoB;AAC/G,kBAAI,iBAAiB,OAAO;AAC1B,uBAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,uBAAuB,qBAAqB;AACnD,YAAI,CAAC,MAAM,QAAQ,mBAAmB,GAAG;AACvC,iBAAwC,aAAa,wEAAwE,IAAI;AACjI,iBAAO;AAAA,QACT;AAEA,iBAAS,IAAI,GAAG,IAAI,oBAAoB,QAAQ,KAAK;AACnD,cAAI,UAAU,oBAAoB,CAAC;AACnC,cAAI,OAAO,YAAY,YAAY;AACjC;AAAA,cACE,gGACc,yBAAyB,OAAO,IAAI,eAAe,IAAI;AAAA,YACvE;AACA,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,gBAAgB,CAAC;AACrB,mBAASA,KAAI,GAAGA,KAAI,oBAAoB,QAAQA,MAAK;AACnD,gBAAIC,WAAU,oBAAoBD,EAAC;AACnC,gBAAI,gBAAgBC,SAAQ,OAAO,UAAU,eAAe,UAAU,cAAc,oBAAoB;AACxG,gBAAI,iBAAiB,MAAM;AACzB,qBAAO;AAAA,YACT;AACA,gBAAI,cAAc,QAAQ,IAAI,cAAc,MAAM,cAAc,GAAG;AACjE,4BAAc,KAAK,cAAc,KAAK,YAAY;AAAA,YACpD;AAAA,UACF;AACA,cAAI,uBAAwB,cAAc,SAAS,IAAK,6BAA6B,cAAc,KAAK,IAAI,IAAI,MAAK;AACrH,iBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,oBAAoB,MAAM,gBAAgB,MAAM,uBAAuB,IAAI;AAAA,QACpJ;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,oBAAoB;AAC3B,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,CAAC,OAAO,MAAM,QAAQ,CAAC,GAAG;AAC5B,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,oBAAoB,MAAM,gBAAgB,2BAA2B;AAAA,UAC9I;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,sBAAsB,eAAe,UAAU,cAAc,KAAK,MAAM;AAC/E,eAAO,IAAI;AAAA,WACR,iBAAiB,iBAAiB,OAAO,WAAW,YAAY,eAAe,MAAM,MAAM,+FACX,OAAO;AAAA,QAC1F;AAAA,MACF;AAEA,eAAS,uBAAuB,YAAY;AAC1C,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,cAAI,aAAa,UAAU;AACzB,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,WAAW,QAAQ,kBAAkB,gBAAgB,wBAAwB;AAAA,UACtK;AACA,mBAAS,OAAO,YAAY;AAC1B,gBAAI,UAAU,WAAW,GAAG;AAC5B,gBAAI,OAAO,YAAY,YAAY;AACjC,qBAAO,sBAAsB,eAAe,UAAU,cAAc,KAAK,eAAe,OAAO,CAAC;AAAA,YAClG;AACA,gBAAI,QAAQ,QAAQ,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK,oBAAoB;AAC3G,gBAAI,OAAO;AACT,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,6BAA6B,YAAY;AAChD,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,cAAI,aAAa,UAAU;AACzB,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,WAAW,QAAQ,kBAAkB,gBAAgB,wBAAwB;AAAA,UACtK;AAEA,cAAI,UAAU,OAAO,CAAC,GAAG,MAAM,QAAQ,GAAG,UAAU;AACpD,mBAAS,OAAO,SAAS;AACvB,gBAAI,UAAU,WAAW,GAAG;AAC5B,gBAAI,IAAI,YAAY,GAAG,KAAK,OAAO,YAAY,YAAY;AACzD,qBAAO,sBAAsB,eAAe,UAAU,cAAc,KAAK,eAAe,OAAO,CAAC;AAAA,YAClG;AACA,gBAAI,CAAC,SAAS;AACZ,qBAAO,IAAI;AAAA,gBACT,aAAa,WAAW,OAAO,eAAe,YAAY,MAAM,oBAAoB,gBAAgB,qBACjF,KAAK,UAAU,MAAM,QAAQ,GAAG,MAAM,IAAI,IAC7D,mBAAmB,KAAK,UAAU,OAAO,KAAK,UAAU,GAAG,MAAM,IAAI;AAAA,cACvE;AAAA,YACF;AACA,gBAAI,QAAQ,QAAQ,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK,oBAAoB;AAC3G,gBAAI,OAAO;AACT,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAEA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,OAAO,WAAW;AACzB,gBAAQ,OAAO,WAAW;AAAA,UACxB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,mBAAO;AAAA,UACT,KAAK;AACH,mBAAO,CAAC;AAAA,UACV,KAAK;AACH,gBAAI,MAAM,QAAQ,SAAS,GAAG;AAC5B,qBAAO,UAAU,MAAM,MAAM;AAAA,YAC/B;AACA,gBAAI,cAAc,QAAQ,eAAe,SAAS,GAAG;AACnD,qBAAO;AAAA,YACT;AAEA,gBAAI,aAAa,cAAc,SAAS;AACxC,gBAAI,YAAY;AACd,kBAAI,WAAW,WAAW,KAAK,SAAS;AACxC,kBAAI;AACJ,kBAAI,eAAe,UAAU,SAAS;AACpC,uBAAO,EAAE,OAAO,SAAS,KAAK,GAAG,MAAM;AACrC,sBAAI,CAAC,OAAO,KAAK,KAAK,GAAG;AACvB,2BAAO;AAAA,kBACT;AAAA,gBACF;AAAA,cACF,OAAO;AAEL,uBAAO,EAAE,OAAO,SAAS,KAAK,GAAG,MAAM;AACrC,sBAAI,QAAQ,KAAK;AACjB,sBAAI,OAAO;AACT,wBAAI,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG;AACrB,6BAAO;AAAA,oBACT;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF,OAAO;AACL,qBAAO;AAAA,YACT;AAEA,mBAAO;AAAA,UACT;AACE,mBAAO;AAAA,QACX;AAAA,MACF;AAEA,eAAS,SAAS,UAAU,WAAW;AAErC,YAAI,aAAa,UAAU;AACzB,iBAAO;AAAA,QACT;AAGA,YAAI,CAAC,WAAW;AACd,iBAAO;AAAA,QACT;AAGA,YAAI,UAAU,eAAe,MAAM,UAAU;AAC3C,iBAAO;AAAA,QACT;AAGA,YAAI,OAAO,WAAW,cAAc,qBAAqB,QAAQ;AAC/D,iBAAO;AAAA,QACT;AAEA,eAAO;AAAA,MACT;AAGA,eAAS,YAAY,WAAW;AAC9B,YAAI,WAAW,OAAO;AACtB,YAAI,MAAM,QAAQ,SAAS,GAAG;AAC5B,iBAAO;AAAA,QACT;AACA,YAAI,qBAAqB,QAAQ;AAI/B,iBAAO;AAAA,QACT;AACA,YAAI,SAAS,UAAU,SAAS,GAAG;AACjC,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAIA,eAAS,eAAe,WAAW;AACjC,YAAI,OAAO,cAAc,eAAe,cAAc,MAAM;AAC1D,iBAAO,KAAK;AAAA,QACd;AACA,YAAI,WAAW,YAAY,SAAS;AACpC,YAAI,aAAa,UAAU;AACzB,cAAI,qBAAqB,MAAM;AAC7B,mBAAO;AAAA,UACT,WAAW,qBAAqB,QAAQ;AACtC,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAIA,eAAS,yBAAyB,OAAO;AACvC,YAAI,OAAO,eAAe,KAAK;AAC/B,gBAAQ,MAAM;AAAA,UACZ,KAAK;AAAA,UACL,KAAK;AACH,mBAAO,QAAQ;AAAA,UACjB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,mBAAO,OAAO;AAAA,UAChB;AACE,mBAAO;AAAA,QACX;AAAA,MACF;AAGA,eAAS,aAAa,WAAW;AAC/B,YAAI,CAAC,UAAU,eAAe,CAAC,UAAU,YAAY,MAAM;AACzD,iBAAO;AAAA,QACT;AACA,eAAO,UAAU,YAAY;AAAA,MAC/B;AAEA,qBAAe,iBAAiB;AAChC,qBAAe,oBAAoB,eAAe;AAClD,qBAAe,YAAY;AAE3B,aAAO;AAAA,IACT;AAAA;AAAA;;;ACjmBA;AAAA;AAOA,QAAI,MAAuC;AACrC,gBAAU;AAIV,4BAAsB;AAC1B,aAAO,UAAU,kCAAqC,QAAQ,WAAW,mBAAmB;AAAA,IAC9F,OAAO;AAGL,aAAO,UAAU,KAAsC;AAAA,IACzD;AAVM;AAIA;AAAA;AAAA;;;ACZN,mBAAkB;AAClB,wBAAsB;AAEtB,SAAS,QAAQ,QAAQ,gBAAgB;AACvC,MAAI,OAAO,OAAO,KAAK,MAAM;AAE7B,MAAI,OAAO,uBAAuB;AAChC,QAAI,UAAU,OAAO,sBAAsB,MAAM;AAEjD,QAAI,gBAAgB;AAClB,gBAAU,QAAQ,OAAO,SAAU,KAAK;AACtC,eAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,MACtD,CAAC;AAAA,IACH;AAEA,SAAK,KAAK,MAAM,MAAM,OAAO;AAAA,EAC/B;AAEA,SAAO;AACT;AAEA,SAAS,eAAe,QAAQ;AAC9B,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,SAAS,UAAU,CAAC,KAAK,OAAO,UAAU,CAAC,IAAI,CAAC;AAEpD,QAAI,IAAI,GAAG;AACT,cAAQ,OAAO,MAAM,GAAG,IAAI,EAAE,QAAQ,SAAU,KAAK;AACnD,wBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,MAC1C,CAAC;AAAA,IACH,WAAW,OAAO,2BAA2B;AAC3C,aAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC;AAAA,IAC1E,OAAO;AACL,cAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AAC7C,eAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,MACjF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,QAAQ,KAAK;AACpB;AAEA,MAAI,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,UAAU;AACvE,cAAU,SAAUC,MAAK;AACvB,aAAO,OAAOA;AAAA,IAChB;AAAA,EACF,OAAO;AACL,cAAU,SAAUA,MAAK;AACvB,aAAOA,QAAO,OAAO,WAAW,cAAcA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,IAC3H;AAAA,EACF;AAEA,SAAO,QAAQ,GAAG;AACpB;AAEA,SAAS,gBAAgB,KAAK,KAAK,OAAO;AACxC,MAAI,OAAO,KAAK;AACd,WAAO,eAAe,KAAK,KAAK;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,OAAO;AACL,QAAI,GAAG,IAAI;AAAA,EACb;AAEA,SAAO;AACT;AAEA,SAAS,8BAA8B,QAAQ,UAAU;AACvD,MAAI,UAAU,KAAM,QAAO,CAAC;AAC5B,MAAI,SAAS,CAAC;AACd,MAAI,aAAa,OAAO,KAAK,MAAM;AACnC,MAAI,KAAK;AAET,OAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACtC,UAAM,WAAW,CAAC;AAClB,QAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAChC,WAAO,GAAG,IAAI,OAAO,GAAG;AAAA,EAC1B;AAEA,SAAO;AACT;AAEA,SAAS,yBAAyB,QAAQ,UAAU;AAClD,MAAI,UAAU,KAAM,QAAO,CAAC;AAE5B,MAAI,SAAS,8BAA8B,QAAQ,QAAQ;AAE3D,MAAI,KAAK;AAET,MAAI,OAAO,uBAAuB;AAChC,QAAI,mBAAmB,OAAO,sBAAsB,MAAM;AAE1D,SAAK,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAC5C,YAAM,iBAAiB,CAAC;AACxB,UAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAChC,UAAI,CAAC,OAAO,UAAU,qBAAqB,KAAK,QAAQ,GAAG,EAAG;AAC9D,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,eAAe,KAAK,GAAG;AAC9B,SAAO,gBAAgB,GAAG,KAAK,sBAAsB,KAAK,CAAC,KAAK,4BAA4B,KAAK,CAAC,KAAK,iBAAiB;AAC1H;AAEA,SAAS,gBAAgB,KAAK;AAC5B,MAAI,MAAM,QAAQ,GAAG,EAAG,QAAO;AACjC;AAEA,SAAS,sBAAsB,KAAK,GAAG;AACrC,MAAI,KAAK,QAAQ,OAAO,WAAW,eAAe,IAAI,OAAO,QAAQ,KAAK,IAAI,YAAY;AAE1F,MAAI,MAAM,KAAM;AAChB,MAAI,OAAO,CAAC;AACZ,MAAI,KAAK;AACT,MAAI,KAAK;AAET,MAAI,IAAI;AAER,MAAI;AACF,SAAK,KAAK,GAAG,KAAK,GAAG,GAAG,EAAE,MAAM,KAAK,GAAG,KAAK,GAAG,OAAO,KAAK,MAAM;AAChE,WAAK,KAAK,GAAG,KAAK;AAElB,UAAI,KAAK,KAAK,WAAW,EAAG;AAAA,IAC9B;AAAA,EACF,SAAS,KAAK;AACZ,SAAK;AACL,SAAK;AAAA,EACP,UAAE;AACA,QAAI;AACF,UAAI,CAAC,MAAM,GAAG,QAAQ,KAAK,KAAM,IAAG,QAAQ,EAAE;AAAA,IAChD,UAAE;AACA,UAAI,GAAI,OAAM;AAAA,IAChB;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,4BAA4B,GAAG,QAAQ;AAC9C,MAAI,CAAC,EAAG;AACR,MAAI,OAAO,MAAM,SAAU,QAAO,kBAAkB,GAAG,MAAM;AAC7D,MAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AACrD,MAAI,MAAM,YAAY,EAAE,YAAa,KAAI,EAAE,YAAY;AACvD,MAAI,MAAM,SAAS,MAAM,MAAO,QAAO,MAAM,KAAK,CAAC;AACnD,MAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC,EAAG,QAAO,kBAAkB,GAAG,MAAM;AACjH;AAEA,SAAS,kBAAkB,KAAK,KAAK;AACnC,MAAI,OAAO,QAAQ,MAAM,IAAI,OAAQ,OAAM,IAAI;AAE/C,WAAS,IAAI,GAAG,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,IAAK,MAAK,CAAC,IAAI,IAAI,CAAC;AAEpE,SAAO;AACT;AAEA,SAAS,mBAAmB;AAC1B,QAAM,IAAI,UAAU,2IAA2I;AACjK;AAEA,IAAI,iBAAiB,SAASC,gBAAe,SAAS,OAAO,IAAI;AAC/D,MAAI,YAAY,CAAC,CAAC;AAClB,MAAI,QAAQ,aAAAC,QAAM,OAAO,EAAE;AAG3B,eAAAA,QAAM,UAAU,WAAY;AAC1B,UAAM,UAAU;AAAA,EAClB,GAAG,CAAC,EAAE,CAAC;AACP,eAAAA,QAAM,UAAU,WAAY;AAC1B,QAAI,CAAC,aAAa,CAAC,SAAS;AAC1B,aAAO,WAAY;AAAA,MAAC;AAAA,IACtB;AAEA,QAAI,cAAc,SAASC,eAAc;AACvC,UAAI,MAAM,SAAS;AACjB,cAAM,QAAQ,MAAM,OAAO,SAAS;AAAA,MACtC;AAAA,IACF;AAEA,YAAQ,GAAG,OAAO,WAAW;AAC7B,WAAO,WAAY;AACjB,cAAQ,IAAI,OAAO,WAAW;AAAA,IAChC;AAAA,EACF,GAAG,CAAC,WAAW,OAAO,SAAS,KAAK,CAAC;AACvC;AAEA,IAAI,cAAc,SAASC,aAAY,OAAO;AAC5C,MAAI,MAAM,aAAAF,QAAM,OAAO,KAAK;AAC5B,eAAAA,QAAM,UAAU,WAAY;AAC1B,QAAI,UAAU;AAAA,EAChB,GAAG,CAAC,KAAK,CAAC;AACV,SAAO,IAAI;AACb;AAEA,IAAI,kBAAkB,SAASG,iBAAgB,KAAK;AAClD,SAAO,QAAQ,QAAQ,QAAQ,GAAG,MAAM;AAC1C;AACA,IAAI,YAAY,SAASC,WAAU,KAAK;AACtC,SAAO,gBAAgB,GAAG,KAAK,OAAO,IAAI,SAAS;AACrD;AAIA,IAAI,WAAW,SAASC,UAAS,KAAK;AACpC,SAAO,gBAAgB,GAAG,KAAK,OAAO,IAAI,aAAa,cAAc,OAAO,IAAI,gBAAgB,cAAc,OAAO,IAAI,wBAAwB,cAAc,OAAO,IAAI,uBAAuB;AACnM;AAEA,IAAI,mBAAmB;AACvB,IAAI,UAAU,SAASC,SAAQ,MAAM,OAAO;AAC1C,MAAI,CAAC,gBAAgB,IAAI,KAAK,CAAC,gBAAgB,KAAK,GAAG;AACrD,WAAO,SAAS;AAAA,EAClB;AAEA,MAAI,YAAY,MAAM,QAAQ,IAAI;AAClC,MAAI,aAAa,MAAM,QAAQ,KAAK;AACpC,MAAI,cAAc,WAAY,QAAO;AACrC,MAAI,kBAAkB,OAAO,UAAU,SAAS,KAAK,IAAI,MAAM;AAC/D,MAAI,mBAAmB,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AACjE,MAAI,oBAAoB,iBAAkB,QAAO;AAGjD,MAAI,CAAC,mBAAmB,CAAC,UAAW,QAAO,SAAS;AACpD,MAAI,WAAW,OAAO,KAAK,IAAI;AAC/B,MAAI,YAAY,OAAO,KAAK,KAAK;AACjC,MAAI,SAAS,WAAW,UAAU,OAAQ,QAAO;AACjD,MAAI,SAAS,CAAC;AAEd,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,GAAG;AAC3C,WAAO,SAAS,CAAC,CAAC,IAAI;AAAA,EACxB;AAEA,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM,GAAG;AAC/C,WAAO,UAAU,EAAE,CAAC,IAAI;AAAA,EAC1B;AAEA,MAAI,UAAU,OAAO,KAAK,MAAM;AAEhC,MAAI,QAAQ,WAAW,SAAS,QAAQ;AACtC,WAAO;AAAA,EACT;AAEA,MAAI,IAAI;AACR,MAAI,IAAI;AAER,MAAI,OAAO,SAASC,MAAK,KAAK;AAC5B,WAAOD,SAAQ,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC;AAAA,EAC/B;AAEA,SAAO,QAAQ,MAAM,IAAI;AAC3B;AAEA,IAAI,+BAA+B,SAASE,8BAA6B,SAAS,aAAa,eAAe;AAC5G,MAAI,CAAC,gBAAgB,OAAO,GAAG;AAC7B,WAAO;AAAA,EACT;AAEA,SAAO,OAAO,KAAK,OAAO,EAAE,OAAO,SAAU,YAAY,KAAK;AAC5D,QAAI,YAAY,CAAC,gBAAgB,WAAW,KAAK,CAAC,QAAQ,QAAQ,GAAG,GAAG,YAAY,GAAG,CAAC;AAExF,QAAI,cAAc,SAAS,GAAG,GAAG;AAC/B,UAAI,WAAW;AACb,gBAAQ,KAAK,oCAAoC,OAAO,KAAK,6BAA6B,CAAC;AAAA,MAC7F;AAEA,aAAO;AAAA,IACT;AAEA,QAAI,CAAC,WAAW;AACd,aAAO;AAAA,IACT;AAEA,WAAO,eAAe,eAAe,CAAC,GAAG,cAAc,CAAC,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,GAAG,KAAK,QAAQ,GAAG,CAAC,CAAC;AAAA,EACxG,GAAG,IAAI;AACT;AAEA,IAAI,yBAAyB;AAI7B,IAAI,iBAAiB,SAASC,gBAAe,aAAa;AACxD,MAAI,WAAW,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAEnF,MAAI,gBAAgB,QAAQ,SAAS,WAAW,GAAG;AACjD,WAAO;AAAA,EACT;AAEA,QAAM,IAAI,MAAM,QAAQ;AAC1B;AAEA,IAAI,kBAAkB,SAASC,iBAAgB,KAAK;AAClD,MAAI,WAAW,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAEnF,MAAI,UAAU,GAAG,GAAG;AAClB,WAAO;AAAA,MACL,KAAK;AAAA,MACL,eAAe,QAAQ,QAAQ,GAAG,EAAE,KAAK,SAAU,QAAQ;AACzD,eAAO,eAAe,QAAQ,QAAQ;AAAA,MACxC,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,SAAS,eAAe,KAAK,QAAQ;AAEzC,MAAI,WAAW,MAAM;AACnB,WAAO;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AAEA,SAAO;AAAA,IACL,KAAK;AAAA,IACL;AAAA,EACF;AACF;AAEA,IAAI,uBAAuB,SAASC,sBAAqB,QAAQ;AAC/D,MAAI,CAAC,UAAU,CAAC,OAAO,oBAAoB,CAAC,OAAO,iBAAiB;AAClE;AAAA,EACF;AAEA,SAAO,iBAAiB;AAAA,IACtB,MAAM;AAAA,IACN,SAAS;AAAA,EACX,CAAC;AAED,SAAO,gBAAgB;AAAA,IACrB,MAAM;AAAA,IACN,SAAS;AAAA,IACT,KAAK;AAAA,EACP,CAAC;AACH;AAEA,IAAI,kBAA+B,aAAAX,QAAM,cAAc,IAAI;AAC3D,gBAAgB,cAAc;AAC9B,IAAI,uBAAuB,SAASY,sBAAqB,KAAK,SAAS;AACrE,MAAI,CAAC,KAAK;AACR,UAAM,IAAI,MAAM,+EAA+E,OAAO,SAAS,6BAA6B,CAAC;AAAA,EAC/I;AAEA,SAAO;AACT;AAYA,IAAI,WAAW,SAASC,UAAS,MAAM;AACrC,MAAI,gBAAgB,KAAK,QACrB,UAAU,KAAK,SACf,WAAW,KAAK;AACpB,MAAI,SAAS,aAAAb,QAAM,QAAQ,WAAY;AACrC,WAAO,gBAAgB,aAAa;AAAA,EACtC,GAAG,CAAC,aAAa,CAAC;AAElB,MAAI,kBAAkB,aAAAA,QAAM,SAAS,WAAY;AAC/C,WAAO;AAAA,MACL,QAAQ,OAAO,QAAQ,SAAS,OAAO,SAAS;AAAA,MAChD,UAAU,OAAO,QAAQ,SAAS,OAAO,OAAO,SAAS,OAAO,IAAI;AAAA,IACtE;AAAA,EACF,CAAC,GACG,mBAAmB,eAAe,iBAAiB,CAAC,GACpD,MAAM,iBAAiB,CAAC,GACxB,aAAa,iBAAiB,CAAC;AAEnC,eAAAA,QAAM,UAAU,WAAY;AAC1B,QAAI,YAAY;AAEhB,QAAI,iBAAiB,SAASc,gBAAe,QAAQ;AACnD,iBAAW,SAAUC,MAAK;AAExB,YAAIA,KAAI,OAAQ,QAAOA;AACvB,eAAO;AAAA,UACL;AAAA,UACA,UAAU,OAAO,SAAS,OAAO;AAAA,QACnC;AAAA,MACF,CAAC;AAAA,IACH;AAGA,QAAI,OAAO,QAAQ,WAAW,CAAC,IAAI,QAAQ;AACzC,aAAO,cAAc,KAAK,SAAU,QAAQ;AAC1C,YAAI,UAAU,WAAW;AAIvB,yBAAe,MAAM;AAAA,QACvB;AAAA,MACF,CAAC;AAAA,IACH,WAAW,OAAO,QAAQ,UAAU,CAAC,IAAI,QAAQ;AAE/C,qBAAe,OAAO,MAAM;AAAA,IAC9B;AAEA,WAAO,WAAY;AACjB,kBAAY;AAAA,IACd;AAAA,EACF,GAAG,CAAC,QAAQ,KAAK,OAAO,CAAC;AAEzB,MAAI,aAAa,YAAY,aAAa;AAC1C,eAAAf,QAAM,UAAU,WAAY;AAC1B,QAAI,eAAe,QAAQ,eAAe,eAAe;AACvD,cAAQ,KAAK,4FAA4F;AAAA,IAC3G;AAAA,EACF,GAAG,CAAC,YAAY,aAAa,CAAC;AAE9B,MAAI,cAAc,YAAY,OAAO;AACrC,eAAAA,QAAM,UAAU,WAAY;AAC1B,QAAI,CAAC,IAAI,UAAU;AACjB;AAAA,IACF;AAEA,QAAI,UAAU,6BAA6B,SAAS,aAAa,CAAC,gBAAgB,OAAO,CAAC;AAE1F,QAAI,SAAS;AACX,UAAI,SAAS,OAAO,OAAO;AAAA,IAC7B;AAAA,EACF,GAAG,CAAC,SAAS,aAAa,IAAI,QAAQ,CAAC;AAEvC,eAAAA,QAAM,UAAU,WAAY;AAC1B,yBAAqB,IAAI,MAAM;AAAA,EACjC,GAAG,CAAC,IAAI,MAAM,CAAC;AACf,SAAoB,aAAAA,QAAM,cAAc,gBAAgB,UAAU;AAAA,IAChE,OAAO;AAAA,EACT,GAAG,QAAQ;AACb;AACA,SAAS,YAAY;AAAA,EACnB,QAAQ,kBAAAgB,QAAU;AAAA,EAClB,SAAS,kBAAAA,QAAU;AACrB;AACA,IAAI,gCAAgC,SAASC,+BAA8B,gBAAgB;AACzF,MAAI,MAAM,aAAAjB,QAAM,WAAW,eAAe;AAC1C,SAAO,qBAAqB,KAAK,cAAc;AACjD;AAKA,IAAI,cAAc,SAASkB,eAAc;AACvC,MAAI,wBAAwB,8BAA8B,qBAAqB,GAC3E,WAAW,sBAAsB;AAErC,SAAO;AACT;AAKA,IAAI,mBAAmB,SAASC,kBAAiB,OAAO;AACtD,MAAI,WAAW,MAAM;AACrB,MAAI,MAAM,8BAA8B,2BAA2B;AAEnE,SAAO,SAAS,GAAG;AACrB;AACA,iBAAiB,YAAY;AAAA,EAC3B,UAAU,kBAAAH,QAAU,KAAK;AAC3B;AAEA,IAAI,YAAY,CAAC,MAAM,SAAS;AAChC,IAAI,2BAAwC,aAAAhB,QAAM,cAAc,IAAI;AACpE,yBAAyB,cAAc;AACvC,IAAI,gCAAgC,SAASoB,+BAA8B,KAAK,SAAS;AACvF,MAAI,CAAC,KAAK;AACR,UAAM,IAAI,MAAM,6FAA6F,OAAO,SAAS,2CAA2C,CAAC;AAAA,EAC3K;AAEA,SAAO;AACT;AACA,IAAI,wBAAqC,aAAApB,QAAM,cAAc,IAAI;AACjE,sBAAsB,cAAc;AACpC,IAAI,oCAAoC,SAASqB,mCAAkC,mBAAmB,cAAc;AAClH,MAAI,CAAC,mBAAmB;AACtB,WAAO;AAAA,EACT;AAEA,oBAAkB;AACd,oBAAkB;AAClB,MAAI,UAAU,yBAAyB,mBAAmB,SAAS;AAEvE,MAAI,CAAC,cAAc;AACjB,WAAO,eAAe,eAAe,CAAC,GAAG,OAAO,GAAG,kBAAkB,QAAQ,CAAC;AAAA,EAChF;AAEA,SAAO,eAAe,eAAe,CAAC,GAAG,OAAO,GAAG,YAAY;AACjE;AACA,IAAI,yBAAyB;AAC7B,IAAI,yBAAyB,SAASC,wBAAuB,MAAM;AACjE,MAAI,gBAAgB,KAAK,QACrB,UAAU,KAAK,SACf,WAAW,KAAK;AACpB,MAAI,SAAS,aAAAtB,QAAM,QAAQ,WAAY;AACrC,WAAO,gBAAgB,eAAe,sBAAsB;AAAA,EAC9D,GAAG,CAAC,aAAa,CAAC;AAElB,MAAI,kBAAkB,aAAAA,QAAM,SAAS,IAAI,GACrC,mBAAmB,eAAe,iBAAiB,CAAC,GACpD,UAAU,iBAAiB,CAAC,GAC5B,aAAa,iBAAiB,CAAC;AAEnC,MAAI,mBAAmB,aAAAA,QAAM,SAAS,WAAY;AAChD,WAAO;AAAA,MACL,QAAQ,OAAO,QAAQ,SAAS,OAAO,SAAS;AAAA,MAChD,mBAAmB;AAAA,IACrB;AAAA,EACF,CAAC,GACG,mBAAmB,eAAe,kBAAkB,CAAC,GACrD,MAAM,iBAAiB,CAAC,GACxB,aAAa,iBAAiB,CAAC;AAEnC,MAAI,iBAAiB,SAASc,gBAAe,QAAQ,mBAAmB;AACtE,eAAW,SAAUC,MAAK;AACxB,UAAIA,KAAI,UAAUA,KAAI,mBAAmB;AACvC,eAAOA;AAAA,MACT;AAEA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAGA,MAAI,8BAA8B,aAAAf,QAAM,OAAO,KAAK;AACpD,eAAAA,QAAM,UAAU,WAAY;AAC1B,QAAI,YAAY;AAEhB,QAAI,OAAO,QAAQ,WAAW,CAAC,IAAI,QAAQ;AACzC,aAAO,cAAc,KAAK,SAAU,QAAQ;AAC1C,YAAI,UAAU,aAAa,CAAC,4BAA4B,SAAS;AAI/D,sCAA4B,UAAU;AACtC,iBAAO,mBAAmB,OAAO,EAAE,KAAK,SAAU,mBAAmB;AACnE,gBAAI,mBAAmB;AACrB,6BAAe,QAAQ,iBAAiB;AACxC,gCAAkB,GAAG,UAAU,UAAU;AAAA,YAC3C;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH,WAAW,OAAO,QAAQ,UAAU,OAAO,UAAU,CAAC,4BAA4B,SAAS;AACzF,kCAA4B,UAAU;AACtC,aAAO,OAAO,mBAAmB,OAAO,EAAE,KAAK,SAAU,mBAAmB;AAC1E,YAAI,mBAAmB;AACrB,yBAAe,OAAO,QAAQ,iBAAiB;AAC/C,4BAAkB,GAAG,UAAU,UAAU;AAAA,QAC3C;AAAA,MACF,CAAC;AAAA,IACH;AAEA,WAAO,WAAY;AACjB,kBAAY;AAAA,IACd;AAAA,EACF,GAAG,CAAC,QAAQ,KAAK,SAAS,UAAU,CAAC;AAErC,MAAI,aAAa,YAAY,aAAa;AAC1C,eAAAA,QAAM,UAAU,WAAY;AAC1B,QAAI,eAAe,QAAQ,eAAe,eAAe;AACvD,cAAQ,KAAK,0GAA0G;AAAA,IACzH;AAAA,EACF,GAAG,CAAC,YAAY,aAAa,CAAC;AAE9B,MAAI,cAAc,YAAY,OAAO;AACrC,eAAAA,QAAM,UAAU,WAAY;AAC1B,QAAI,uBAAuB;AAE3B,QAAI,CAAC,IAAI,mBAAmB;AAC1B;AAAA,IACF;AAEA,QAAI,QAAQ,gBAAgB,CAAC,gBAAgB,WAAW,KAAK,CAAC,QAAQ,QAAQ,cAAc,YAAY,YAAY,GAAG;AACrH,cAAQ,KAAK,2EAA2E;AAAA,IAC1F;AAEA,QAAI,qBAAqB,gBAAgB,QAAQ,gBAAgB,SAAS,UAAU,wBAAwB,YAAY,qBAAqB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB;AACvN,QAAI,oBAAoB,YAAY,QAAQ,YAAY,SAAS,UAAU,wBAAwB,QAAQ,qBAAqB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB;AAE1M,QAAI,qBAAqB,CAAC,QAAQ,mBAAmB,kBAAkB,GAAG;AACxE,UAAI,kBAAkB,iBAAiB,iBAAiB;AAAA,IAC1D;AAAA,EACF,GAAG,CAAC,SAAS,aAAa,IAAI,iBAAiB,CAAC;AAEhD,eAAAA,QAAM,UAAU,WAAY;AAC1B,yBAAqB,IAAI,MAAM;AAAA,EACjC,GAAG,CAAC,IAAI,MAAM,CAAC;AACf,MAAI,6BAA6B,aAAAA,QAAM,QAAQ,WAAY;AACzD,WAAO,kCAAkC,IAAI,mBAAmB,OAAO;AAAA,EACzE,GAAG,CAAC,IAAI,mBAAmB,OAAO,CAAC;AAEnC,MAAI,CAAC,IAAI,mBAAmB;AAC1B,WAAO;AAAA,EACT;AAEA,SAAoB,aAAAA,QAAM,cAAc,yBAAyB,UAAU;AAAA,IACzE,OAAO;AAAA,EACT,GAAgB,aAAAA,QAAM,cAAc,sBAAsB,UAAU;AAAA,IAClE,OAAO;AAAA,EACT,GAAG,QAAQ,CAAC;AACd;AACA,uBAAuB,YAAY;AAAA,EACjC,QAAQ,kBAAAgB,QAAU;AAAA,EAClB,SAAS,kBAAAA,QAAU,MAAM;AAAA,IACvB,cAAc,kBAAAA,QAAU,OAAO;AAAA,IAC/B,iBAAiB,kBAAAA,QAAU;AAAA,EAC7B,CAAC,EAAE;AACL;AACA,IAAI,yCAAyC,SAASO,wCAAuC,eAAe;AAC1G,MAAI,MAAM,aAAAvB,QAAM,WAAW,wBAAwB;AACnD,SAAO,8BAA8B,KAAK,aAAa;AACzD;AACA,IAAI,mDAAmD,SAASwB,kDAAiD,eAAe;AAC9H,MAAI,2BAA2B,aAAAxB,QAAM,WAAW,wBAAwB;AACxE,MAAI,kBAAkB,aAAAA,QAAM,WAAW,eAAe;AAEtD,MAAI,4BAA4B,iBAAiB;AAC/C,UAAM,IAAI,MAAM,6CAA6C,OAAO,eAAe,6DAA6D,CAAC;AAAA,EACnJ;AAEA,MAAI,0BAA0B;AAC5B,WAAO,8BAA8B,0BAA0B,aAAa;AAAA,EAC9E;AAEA,SAAO,qBAAqB,iBAAiB,aAAa;AAC5D;AACA,IAAI,oBAAoB,SAASyB,qBAAoB;AAEnD,yCAAuC,2BAA2B;AAClE,MAAI,MAAM,aAAAzB,QAAM,WAAW,qBAAqB;AAEhD,MAAI,CAAC,KAAK;AACR,UAAM,IAAI,MAAM,sJAAsJ;AAAA,EACxK;AAEA,SAAO;AACT;AAEA,IAAI,cAAc,SAAS0B,aAAY,KAAK;AAC1C,SAAO,IAAI,OAAO,CAAC,EAAE,YAAY,IAAI,IAAI,MAAM,CAAC;AAClD;AAEA,IAAI,yBAAyB,SAASC,wBAAuB,MAAMC,WAAU;AAC3E,MAAI,cAAc,GAAG,OAAO,YAAY,IAAI,GAAG,SAAS;AAExD,MAAI,gBAAgB,SAASC,eAAc,MAAM;AAC/C,QAAI,KAAK,KAAK,IACV,YAAY,KAAK,WACjB,eAAe,KAAK,SACpB,UAAU,iBAAiB,SAAS,CAAC,IAAI,cACzC,SAAS,KAAK,QACd,UAAU,KAAK,SACf,UAAU,KAAK,SACf,WAAW,KAAK,UAChB,WAAW,KAAK,UAChB,UAAU,KAAK,SACf,cAAc,KAAK,aACnB,gBAAgB,KAAK,eACrB,mBAAmB,KAAK,kBACxB,YAAY,KAAK,WACjB,WAAW,KAAK,UAChB,0BAA0B,KAAK,yBAC/B,uBAAuB,KAAK;AAChC,QAAI,MAAM,iDAAiD,WAAW,OAAO,aAAa,GAAG,CAAC;AAC9F,QAAI,WAAW,cAAc,MAAM,IAAI,WAAW;AAClD,QAAI,oBAAoB,uBAAuB,MAAM,IAAI,oBAAoB;AAE7E,QAAI,kBAAkB,aAAA7B,QAAM,SAAS,IAAI,GACrC,mBAAmB,eAAe,iBAAiB,CAAC,GACpD,UAAU,iBAAiB,CAAC,GAC5B,aAAa,iBAAiB,CAAC;AAEnC,QAAI,aAAa,aAAAA,QAAM,OAAO,IAAI;AAClC,QAAI,UAAU,aAAAA,QAAM,OAAO,IAAI;AAI/B,mBAAe,SAAS,QAAQ,MAAM;AACtC,mBAAe,SAAS,SAAS,OAAO;AACxC,mBAAe,SAAS,UAAU,QAAQ;AAC1C,mBAAe,SAAS,SAAS,OAAO;AACxC,mBAAe,SAAS,aAAa,WAAW;AAChD,mBAAe,SAAS,eAAe,aAAa;AACpD,mBAAe,SAAS,kBAAkB,gBAAgB;AAC1D,mBAAe,SAAS,WAAW,SAAS;AAC5C,mBAAe,SAAS,UAAU,QAAQ;AAC1C,mBAAe,SAAS,yBAAyB,uBAAuB;AACxE,mBAAe,SAAS,sBAAsB,oBAAoB;AAClE,mBAAe,SAAS,UAAU,QAAQ;AAC1C,QAAI;AAEJ,QAAI,SAAS;AACX,UAAI,SAAS,mBAAmB;AAE9B,wBAAgB;AAAA,MAClB,OAAO;AAEL,wBAAgB,SAAS8B,iBAAgB;AACvC,kBAAQ,OAAO;AAAA,QACjB;AAAA,MACF;AAAA,IACF;AAEA,mBAAe,SAAS,SAAS,aAAa;AAC9C,iBAAA9B,QAAM,gBAAgB,WAAY;AAChC,UAAI,WAAW,YAAY,QAAQ,QAAQ,YAAY,SAAS,YAAY,oBAAoB;AAC9F,YAAI,aAAa;AAEjB,YAAI,mBAAmB;AACrB,uBAAa,kBAAkB,cAAc,MAAM,OAAO;AAAA,QAC5D,WAAW,UAAU;AACnB,uBAAa,SAAS,OAAO,MAAM,OAAO;AAAA,QAC5C;AAGA,mBAAW,UAAU;AAErB,mBAAW,UAAU;AAErB,YAAI,YAAY;AACd,qBAAW,MAAM,QAAQ,OAAO;AAAA,QAClC;AAAA,MACF;AAAA,IACF,GAAG,CAAC,UAAU,mBAAmB,OAAO,CAAC;AACzC,QAAI,cAAc,YAAY,OAAO;AACrC,iBAAAA,QAAM,UAAU,WAAY;AAC1B,UAAI,CAAC,WAAW,SAAS;AACvB;AAAA,MACF;AAEA,UAAI,UAAU,6BAA6B,SAAS,aAAa,CAAC,gBAAgB,CAAC;AAEnF,UAAI,WAAW,YAAY,WAAW,SAAS;AAC7C,mBAAW,QAAQ,OAAO,OAAO;AAAA,MACnC;AAAA,IACF,GAAG,CAAC,SAAS,WAAW,CAAC;AACzB,iBAAAA,QAAM,gBAAgB,WAAY;AAChC,aAAO,WAAY;AACjB,YAAI,WAAW,WAAW,OAAO,WAAW,QAAQ,YAAY,YAAY;AAC1E,cAAI;AACF,uBAAW,QAAQ,QAAQ;AAC3B,uBAAW,UAAU;AAAA,UACvB,SAAS,OAAO;AAAA,UAChB;AAAA,QACF;AAAA,MACF;AAAA,IACF,GAAG,CAAC,CAAC;AACL,WAAoB,aAAAA,QAAM,cAAc,OAAO;AAAA,MAC7C;AAAA,MACA;AAAA,MACA,KAAK;AAAA,IACP,CAAC;AAAA,EACH;AAGA,MAAI,gBAAgB,SAAS+B,eAAc,OAAO;AAChD,qDAAiD,WAAW,OAAO,aAAa,GAAG,CAAC;AACpF,QAAI,KAAK,MAAM,IACX,YAAY,MAAM;AACtB,WAAoB,aAAA/B,QAAM,cAAc,OAAO;AAAA,MAC7C;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAEA,MAAI,UAAU4B,YAAW,gBAAgB;AACzC,UAAQ,YAAY;AAAA,IAClB,IAAI,kBAAAZ,QAAU;AAAA,IACd,WAAW,kBAAAA,QAAU;AAAA,IACrB,UAAU,kBAAAA,QAAU;AAAA,IACpB,QAAQ,kBAAAA,QAAU;AAAA,IAClB,SAAS,kBAAAA,QAAU;AAAA,IACnB,SAAS,kBAAAA,QAAU;AAAA,IACnB,UAAU,kBAAAA,QAAU;AAAA,IACpB,SAAS,kBAAAA,QAAU;AAAA,IACnB,aAAa,kBAAAA,QAAU;AAAA,IACvB,eAAe,kBAAAA,QAAU;AAAA,IACzB,kBAAkB,kBAAAA,QAAU;AAAA,IAC5B,WAAW,kBAAAA,QAAU;AAAA,IACrB,UAAU,kBAAAA,QAAU;AAAA,IACpB,yBAAyB,kBAAAA,QAAU;AAAA,IACnC,sBAAsB,kBAAAA,QAAU;AAAA,IAChC,SAAS,kBAAAA,QAAU;AAAA,EACrB;AACA,UAAQ,cAAc;AACtB,UAAQ,gBAAgB;AACxB,SAAO;AACT;AAEA,IAAI,WAAW,OAAO,WAAW;AAEjC,IAAI,0BAAuC,aAAAhB,QAAM,cAAc,IAAI;AACnE,wBAAwB,cAAc;AACtC,IAAI,6BAA6B,SAASgC,8BAA6B;AACrE,MAAI,MAAM,aAAAhC,QAAM,WAAW,uBAAuB;AAElD,MAAI,CAAC,KAAK;AACR,UAAM,IAAI,MAAM,mEAAmE;AAAA,EACrF;AAEA,SAAO;AACT;AACA,IAAI,uBAAuB;AAC3B,IAAI,2BAA2B,SAASiC,0BAAyB,MAAM;AACrE,MAAI,gBAAgB,KAAK,QACrB,UAAU,KAAK,SACf,WAAW,KAAK;AACpB,MAAI,SAAS,aAAAjC,QAAM,QAAQ,WAAY;AACrC,WAAO,gBAAgB,eAAe,oBAAoB;AAAA,EAC5D,GAAG,CAAC,aAAa,CAAC;AAClB,MAAI,0BAA0B,aAAAA,QAAM,OAAO,IAAI;AAC/C,MAAI,eAAe,aAAAA,QAAM,OAAO,IAAI;AAEpC,MAAI,kBAAkB,aAAAA,QAAM,SAAS;AAAA,IACnC,kBAAkB;AAAA,EACpB,CAAC,GACG,mBAAmB,eAAe,iBAAiB,CAAC,GACpD,MAAM,iBAAiB,CAAC,GACxB,aAAa,iBAAiB,CAAC;AAEnC,eAAAA,QAAM,UAAU,WAAY;AAE1B,QAAI,aAAa,WAAW,wBAAwB,SAAS;AAC3D;AAAA,IACF;AAEA,QAAI,mCAAmC,SAASkC,kCAAiC,QAAQ;AACvF,UAAI,aAAa,WAAW,wBAAwB,QAAS;AAC7D,mBAAa,UAAU;AACvB,8BAAwB,UAAU,aAAa,QAAQ,qBAAqB,OAAO,EAAE,KAAK,SAAU,kBAAkB;AACpH,mBAAW;AAAA,UACT;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAGA,QAAI,OAAO,QAAQ,WAAW,CAAC,aAAa,YAAY,QAAQ,gBAAgB,QAAQ,oBAAoB;AAC1G,aAAO,cAAc,KAAK,SAAU,QAAQ;AAC1C,YAAI,QAAQ;AACV,2CAAiC,MAAM;AAAA,QACzC;AAAA,MACF,CAAC;AAAA,IACH,WAAW,OAAO,QAAQ,UAAU,CAAC,aAAa,YAAY,QAAQ,gBAAgB,QAAQ,oBAAoB;AAEhH,uCAAiC,OAAO,MAAM;AAAA,IAChD;AAAA,EACF,GAAG,CAAC,QAAQ,SAAS,KAAK,YAAY,CAAC;AACvC,eAAAlC,QAAM,UAAU,WAAY;AAE1B,WAAO,WAAY;AAEjB,UAAI,IAAI,kBAAkB;AACxB,gCAAwB,UAAU;AAClC,YAAI,iBAAiB,QAAQ;AAAA,MAC/B,WAAW,wBAAwB,SAAS;AAI1C,gCAAwB,QAAQ,KAAK,WAAY;AAC/C,kCAAwB,UAAU;AAElC,cAAI,IAAI,kBAAkB;AACxB,gBAAI,iBAAiB,QAAQ;AAAA,UAC/B;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,GAAG,CAAC,IAAI,gBAAgB,CAAC;AAEzB,eAAAA,QAAM,UAAU,WAAY;AAC1B,yBAAqB,YAAY;AAAA,EACnC,GAAG,CAAC,YAAY,CAAC;AAIjB,MAAI,aAAa,YAAY,aAAa;AAC1C,eAAAA,QAAM,UAAU,WAAY;AAC1B,QAAI,eAAe,QAAQ,eAAe,eAAe;AACvD,cAAQ,KAAK,4GAA4G;AAAA,IAC3H;AAAA,EACF,GAAG,CAAC,YAAY,aAAa,CAAC;AAE9B,MAAI,cAAc,YAAY,OAAO;AACrC,eAAAA,QAAM,UAAU,WAAY;AAC1B,QAAI,eAAe,MAAM;AACvB;AAAA,IACF;AAEA,QAAI,WAAW,MAAM;AACnB,cAAQ,KAAK,mGAAmG;AAChH;AAAA,IACF;AAEA,QAAI,QAAQ,iBAAiB,UAAa,QAAQ,sBAAsB,QAAW;AACjF,cAAQ,KAAK,yIAAyI;AAAA,IACxJ;AAEA,QAAI,YAAY,gBAAgB,QAAQ,QAAQ,iBAAiB,YAAY,cAAc;AACzF,cAAQ,KAAK,mLAAmL;AAAA,IAClM;AAEA,QAAI,YAAY,qBAAqB,QAAQ,QAAQ,sBAAsB,YAAY,mBAAmB;AACxG,cAAQ,KAAK,mLAAmL;AAAA,IAClM;AAEA,QAAI,YAAY,cAAc,QAAQ,QAAQ,eAAe,YAAY,YAAY;AACnF,cAAQ,KAAK,gHAAgH;AAAA,IAC/H;AAEA,QAAI,YAAY,2BAA2B,QAAQ,QAAQ,4BAA4B,YAAY,yBAAyB;AAC1H,cAAQ,KAAK,6HAA6H;AAAA,IAC5I;AAEA,QAAI,YAAY,qBAAqB,QAAQ,QAAQ,sBAAsB,YAAY,mBAAmB;AACxG,cAAQ,KAAK,uHAAuH;AAAA,IACtI;AAAA,EACF,GAAG,CAAC,aAAa,OAAO,CAAC;AACzB,SAAoB,aAAAA,QAAM,cAAc,wBAAwB,UAAU;AAAA,IACxE,OAAO;AAAA,EACT,GAAG,QAAQ;AACb;AAEA,IAAI,gCAAgC,SAASmC,+BAA8B,MAAM;AAC/E,MAAI,KAAK,KAAK,IACV,YAAY,KAAK;AAErB,MAAI,wBAAwB,2BAA2B,GACnD,mBAAmB,sBAAsB;AAE7C,MAAI,YAAY,aAAAnC,QAAM,OAAO,KAAK;AAClC,MAAI,UAAU,aAAAA,QAAM,OAAO,IAAI;AAC/B,eAAAA,QAAM,gBAAgB,WAAY;AAChC,QAAI,CAAC,UAAU,WAAW,oBAAoB,QAAQ,YAAY,MAAM;AACtE,uBAAiB,MAAM,QAAQ,OAAO;AACtC,gBAAU,UAAU;AAAA,IACtB;AAGA,WAAO,WAAY;AACjB,UAAI,UAAU,WAAW,kBAAkB;AACzC,YAAI;AACF,2BAAiB,QAAQ;AACzB,oBAAU,UAAU;AAAA,QACtB,SAAS,GAAG;AAAA,QAMZ;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG,CAAC,gBAAgB,CAAC;AACrB,SAAoB,aAAAA,QAAM,cAAc,OAAO;AAAA,IAC7C,KAAK;AAAA,IACL;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAGA,IAAI,gCAAgC,SAASoC,+BAA8B,OAAO;AAChF,MAAI,KAAK,MAAM,IACX,YAAY,MAAM;AAEtB,6BAA2B;AAC3B,SAAoB,aAAApC,QAAM,cAAc,OAAO;AAAA,IAC7C;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAEA,IAAI,mBAAmB,WAAW,gCAAgC;AAMlE,IAAI,YAAY,SAASqC,aAAY;AACnC,MAAI,wBAAwB,iDAAiD,mBAAmB,GAC5F,SAAS,sBAAsB;AAEnC,SAAO;AACT;AASA,IAAI,uBAAuB,uBAAuB,iBAAiB,QAAQ;AAK3E,IAAI,cAAc,uBAAuB,QAAQ,QAAQ;AAKzD,IAAI,oBAAoB,uBAAuB,cAAc,QAAQ;AAKrE,IAAI,oBAAoB,uBAAuB,cAAc,QAAQ;AAKrE,IAAI,iBAAiB,uBAAuB,WAAW,QAAQ;AAK/D,IAAI,iBAAiB,uBAAuB,WAAW,QAAQ;AAK/D,IAAI,cAAc,uBAAuB,QAAQ,QAAQ;AAKzD,IAAI,mBAAmB,uBAAuB,aAAa,QAAQ;AAKnE,IAAI,iBAAiB,uBAAuB,WAAW,QAAQ;AAK/D,IAAI,iBAAiB,uBAAuB,WAAW,QAAQ;AAC/D,IAAI,iBAAiB,uBAAuB,WAAW,QAAQ;AAK/D,IAAI,yBAAyB,uBAAuB,mBAAmB,QAAQ;AAM/E,IAAI,0BAA0B,uBAAuB,oBAAoB,QAAQ;AAKjF,IAAI,8BAA8B,uBAAuB,wBAAwB,QAAQ;AAKzF,IAAI,4BAA4B,uBAAuB,sBAAsB,QAAQ;AAKrF,IAAI,iBAAiB,uBAAuB,WAAW,QAAQ;AAQ/D,IAAI,yBAAyB,uBAAuB,mBAAmB,QAAQ;AAK/E,IAAI,gCAAgC,uBAAuB,0BAA0B,QAAQ;AAK7F,IAAI,uBAAuB,uBAAuB,iBAAiB,QAAQ;AAK3E,IAAI,iCAAiC,uBAAuB,2BAA2B,QAAQ;", "names": ["i", "checker", "obj", "useAttachEvent", "React", "decoratedCb", "usePrevious", "isUnknownObject", "isPromise", "isStripe", "isEqual", "pred", "extractAllowedOptionsUpdates", "validateStripe", "parseStripeProp", "registerWithStripeJs", "parseElementsContext", "Elements", "safeSetContext", "ctx", "PropTypes", "useElementsContextWithUseCase", "useElements", "ElementsConsumer", "parseCustomCheckoutSdkContext", "extractCustomCheckoutContextValue", "CustomCheckoutProvider", "useCustomCheckoutSdkContextWithUseCase", "useElementsOrCustomCheckoutSdkContextWithUseCase", "useCustomCheckout", "capitalized", "createElementComponent", "isServer", "ClientElement", "readyCallback", "ServerElement", "useEmbeddedCheckoutContext", "EmbeddedCheckoutProvider", "setStripeAndInitEmbeddedCheckout", "EmbeddedCheckoutClientElement", "EmbeddedCheckoutServerElement", "useStripe"]}