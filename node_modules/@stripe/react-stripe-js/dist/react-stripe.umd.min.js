!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).ReactStripe={},e.React)}(this,(function(e,t){"use strict";function n(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function r(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach((function(t){u(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function u(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function c(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},u=Object.keys(e);for(r=0;r<u.length;r++)n=u[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(e);for(r=0;r<u.length;r++)n=u[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function i(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=e&&("undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"]);if(null==n)return;var r,o,u=[],c=!0,i=!1;try{for(n=n.call(e);!(c=(r=n.next()).done)&&(u.push(r.value),!t||u.length!==t);c=!0);}catch(e){i=!0,o=e}finally{try{c||null==n.return||n.return()}finally{if(i)throw o}}return u}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return s(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return s(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function a(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var l,p,f,d,m={exports:{}};m.exports=function(){if(d)return f;d=1;var e=p?l:(p=1,l="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED");function t(){}function n(){}return n.resetWarningCache=t,f=function(){function r(t,n,r,o,u,c){if(c!==e){var i=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw i.name="Invariant Violation",i}}function o(){return r}r.isRequired=r;var u={array:r,bool:r,func:r,number:r,object:r,string:r,symbol:r,any:r,arrayOf:o,element:r,elementType:r,instanceOf:o,node:r,objectOf:o,oneOf:o,oneOfType:o,shape:o,exact:o,checkPropTypes:n,resetWarningCache:t};return u.PropTypes=u,u}}()();var h=a(m.exports),y=function(e,n,r){var o=!!r,u=t.useRef(r);t.useEffect((function(){u.current=r}),[r]),t.useEffect((function(){if(!o||!e)return function(){};var t=function(){u.current&&u.current.apply(u,arguments)};return e.on(n,t),function(){e.off(n,t)}}),[o,n,e,u])},C=function(e){var n=t.useRef(e);return t.useEffect((function(){n.current=e}),[e]),n.current},v=function(e){return null!==e&&"object"===o(e)},g="[object Object]",E=function e(t,n){if(!v(t)||!v(n))return t===n;var r=Array.isArray(t);if(r!==Array.isArray(n))return!1;var o=Object.prototype.toString.call(t)===g;if(o!==(Object.prototype.toString.call(n)===g))return!1;if(!o&&!r)return t===n;var u=Object.keys(t),c=Object.keys(n);if(u.length!==c.length)return!1;for(var i={},s=0;s<u.length;s+=1)i[u[s]]=!0;for(var a=0;a<c.length;a+=1)i[c[a]]=!0;var l=Object.keys(i);if(l.length!==u.length)return!1;var p=t,f=n;return l.every((function(t){return e(p[t],f[t])}))},b=function(e,t,n){return v(e)?Object.keys(e).reduce((function(o,c){var i=!v(t)||!E(e[c],t[c]);return n.includes(c)?(i&&console.warn("Unsupported prop change: options.".concat(c," is not a mutable property.")),o):i?r(r({},o||{}),{},u({},c,e[c])):o}),null):null},k="Invalid prop `stripe` supplied to `Elements`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.",S=function(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:k;if(null===e||v(t=e)&&"function"==typeof t.elements&&"function"==typeof t.createToken&&"function"==typeof t.createPaymentMethod&&"function"==typeof t.confirmCardPayment)return e;throw new Error(n)},P=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:k;if(function(e){return v(e)&&"function"==typeof e.then}(e))return{tag:"async",stripePromise:Promise.resolve(e).then((function(e){return S(e,t)}))};var n=S(e,t);return null===n?{tag:"empty"}:{tag:"sync",stripe:n}},w=function(e){e&&e._registerWrapper&&e.registerAppInfo&&(e._registerWrapper({name:"react-stripe-js",version:"2.8.1"}),e.registerAppInfo({name:"react-stripe-js",version:"2.8.1",url:"https://stripe.com/docs/stripe-js/react"}))},O=t.createContext(null);O.displayName="ElementsContext";var j=function(e,t){if(!e)throw new Error("Could not find Elements context; You need to wrap the part of your app that ".concat(t," in an <Elements> provider."));return e},x=function(e){var n=e.stripe,r=e.options,o=e.children,u=t.useMemo((function(){return P(n)}),[n]),c=i(t.useState((function(){return{stripe:"sync"===u.tag?u.stripe:null,elements:"sync"===u.tag?u.stripe.elements(r):null}})),2),s=c[0],a=c[1];t.useEffect((function(){var e=!0,t=function(e){a((function(t){return t.stripe?t:{stripe:e,elements:e.elements(r)}}))};return"async"!==u.tag||s.stripe?"sync"!==u.tag||s.stripe||t(u.stripe):u.stripePromise.then((function(n){n&&e&&t(n)})),function(){e=!1}}),[u,s,r]);var l=C(n);t.useEffect((function(){null!==l&&l!==n&&console.warn("Unsupported prop change on Elements: You cannot change the `stripe` prop after setting it.")}),[l,n]);var p=C(r);return t.useEffect((function(){if(s.elements){var e=b(r,p,["clientSecret","fonts"]);e&&s.elements.update(e)}}),[r,p,s.elements]),t.useEffect((function(){w(s.stripe)}),[s.stripe]),t.createElement(O.Provider,{value:s},o)};x.propTypes={stripe:h.any,options:h.object};var A=function(e){var n=t.useContext(O);return j(n,e)},R=function(e){return(0,e.children)(A("mounts <ElementsConsumer>"))};R.propTypes={children:h.func.isRequired};var I=["on","session"],N=t.createContext(null);N.displayName="CustomCheckoutSdkContext";var T=function(e,t){if(!e)throw new Error("Could not find CustomCheckoutProvider context; You need to wrap the part of your app that ".concat(t," in an <CustomCheckoutProvider> provider."));return e},U=t.createContext(null);U.displayName="CustomCheckoutContext";var _=function(e){var n=e.stripe,o=e.options,u=e.children,s=t.useMemo((function(){return P(n,"Invalid prop `stripe` supplied to `CustomCheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.")}),[n]),a=i(t.useState(null),2),l=a[0],p=a[1],f=i(t.useState((function(){return{stripe:"sync"===s.tag?s.stripe:null,customCheckoutSdk:null}})),2),d=f[0],m=f[1],h=function(e,t){m((function(n){return n.stripe&&n.customCheckoutSdk?n:{stripe:e,customCheckoutSdk:t}}))},y=t.useRef(!1);t.useEffect((function(){var e=!0;return"async"!==s.tag||d.stripe?"sync"===s.tag&&s.stripe&&!y.current&&(y.current=!0,s.stripe.initCustomCheckout(o).then((function(e){e&&(h(s.stripe,e),e.on("change",p))}))):s.stripePromise.then((function(t){t&&e&&!y.current&&(y.current=!0,t.initCustomCheckout(o).then((function(e){e&&(h(t,e),e.on("change",p))})))})),function(){e=!1}}),[s,d,o,p]);var g=C(n);t.useEffect((function(){null!==g&&g!==n&&console.warn("Unsupported prop change on CustomCheckoutProvider: You cannot change the `stripe` prop after setting it.")}),[g,n]);var b=C(o);t.useEffect((function(){var e,t;if(d.customCheckoutSdk){!o.clientSecret||v(b)||E(o.clientSecret,b.clientSecret)||console.warn("Unsupported prop change: options.client_secret is not a mutable property.");var n=null==b||null===(e=b.elementsOptions)||void 0===e?void 0:e.appearance,r=null==o||null===(t=o.elementsOptions)||void 0===t?void 0:t.appearance;r&&!E(r,n)&&d.customCheckoutSdk.changeAppearance(r)}}),[o,b,d.customCheckoutSdk]),t.useEffect((function(){w(d.stripe)}),[d.stripe]);var k=t.useMemo((function(){return e=d.customCheckoutSdk,t=l,e?(e.on,e.session,r(r({},c(e,I)),t||e.session())):null;var e,t}),[d.customCheckoutSdk,l]);return d.customCheckoutSdk?t.createElement(N.Provider,{value:d},t.createElement(U.Provider,{value:k},u)):null};_.propTypes={stripe:h.any,options:h.shape({clientSecret:h.string.isRequired,elementsOptions:h.object}).isRequired};var B=function(e){var n=t.useContext(N),r=t.useContext(O);if(n&&r)throw new Error("You cannot wrap the part of your app that ".concat(e," in both <CustomCheckoutProvider> and <Elements> providers."));return n?T(n,e):j(r,e)},M=function(e,n){var r,o="".concat((r=e).charAt(0).toUpperCase()+r.slice(1),"Element"),u=n?function(e){B("mounts <".concat(o,">"));var n=e.id,r=e.className;return t.createElement("div",{id:n,className:r})}:function(n){var r,u=n.id,c=n.className,s=n.options,a=void 0===s?{}:s,l=n.onBlur,p=n.onFocus,f=n.onReady,d=n.onChange,m=n.onEscape,h=n.onClick,v=n.onLoadError,g=n.onLoaderStart,E=n.onNetworksChange,k=n.onConfirm,S=n.onCancel,P=n.onShippingAddressChange,w=n.onShippingRateChange,O=B("mounts <".concat(o,">")),j="elements"in O?O.elements:null,x="customCheckoutSdk"in O?O.customCheckoutSdk:null,A=i(t.useState(null),2),R=A[0],I=A[1],N=t.useRef(null),T=t.useRef(null);y(R,"blur",l),y(R,"focus",p),y(R,"escape",m),y(R,"click",h),y(R,"loaderror",v),y(R,"loaderstart",g),y(R,"networkschange",E),y(R,"confirm",k),y(R,"cancel",S),y(R,"shippingaddresschange",P),y(R,"shippingratechange",w),y(R,"change",d),f&&(r="expressCheckout"===e?f:function(){f(R)}),y(R,"ready",r),t.useLayoutEffect((function(){if(null===N.current&&null!==T.current&&(j||x)){var t=null;x?t=x.createElement(e,a):j&&(t=j.create(e,a)),N.current=t,I(t),t&&t.mount(T.current)}}),[j,x,a]);var U=C(a);return t.useEffect((function(){if(N.current){var e=b(a,U,["paymentRequest"]);e&&"update"in N.current&&N.current.update(e)}}),[a,U]),t.useLayoutEffect((function(){return function(){if(N.current&&"function"==typeof N.current.destroy)try{N.current.destroy(),N.current=null}catch(e){}}}),[]),t.createElement("div",{id:u,className:c,ref:T})};return u.propTypes={id:h.string,className:h.string,onChange:h.func,onBlur:h.func,onFocus:h.func,onReady:h.func,onEscape:h.func,onClick:h.func,onLoadError:h.func,onLoaderStart:h.func,onNetworksChange:h.func,onConfirm:h.func,onCancel:h.func,onShippingAddressChange:h.func,onShippingRateChange:h.func,options:h.object},u.displayName=o,u.__elementType=e,u},Y="undefined"==typeof window,L=t.createContext(null);L.displayName="EmbeddedCheckoutProviderContext";var D=function(){var e=t.useContext(L);if(!e)throw new Error("<EmbeddedCheckout> must be used within <EmbeddedCheckoutProvider>");return e},q=Y?function(e){var n=e.id,r=e.className;return D(),t.createElement("div",{id:n,className:r})}:function(e){var n=e.id,r=e.className,o=D().embeddedCheckout,u=t.useRef(!1),c=t.useRef(null);return t.useLayoutEffect((function(){return!u.current&&o&&null!==c.current&&(o.mount(c.current),u.current=!0),function(){if(u.current&&o)try{o.unmount(),u.current=!1}catch(e){}}}),[o]),t.createElement("div",{ref:c,id:n,className:r})},W=M("auBankAccount",Y),F=M("card",Y),H=M("cardNumber",Y),V=M("cardExpiry",Y),$=M("cardCvc",Y),z=M("fpxBank",Y),G=M("iban",Y),J=M("idealBank",Y),K=M("p24Bank",Y),Q=M("epsBank",Y),X=M("payment",Y),Z=M("expressCheckout",Y),ee=M("currencySelector",Y),te=M("paymentRequestButton",Y),ne=M("linkAuthentication",Y),re=M("address",Y),oe=M("shippingAddress",Y),ue=M("paymentMethodMessaging",Y),ce=M("affirmMessage",Y),ie=M("afterpayClearpayMessage",Y);e.AddressElement=re,e.AffirmMessageElement=ce,e.AfterpayClearpayMessageElement=ie,e.AuBankAccountElement=W,e.CardCvcElement=$,e.CardElement=F,e.CardExpiryElement=V,e.CardNumberElement=H,e.CurrencySelectorElement=ee,e.CustomCheckoutProvider=_,e.Elements=x,e.ElementsConsumer=R,e.EmbeddedCheckout=q,e.EmbeddedCheckoutProvider=function(e){var n=e.stripe,r=e.options,o=e.children,u=t.useMemo((function(){return P(n,"Invalid prop `stripe` supplied to `EmbeddedCheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.")}),[n]),c=t.useRef(null),s=t.useRef(null),a=i(t.useState({embeddedCheckout:null}),2),l=a[0],p=a[1];t.useEffect((function(){if(!s.current&&!c.current){var e=function(e){s.current||c.current||(s.current=e,c.current=s.current.initEmbeddedCheckout(r).then((function(e){p({embeddedCheckout:e})})))};"async"!==u.tag||s.current||!r.clientSecret&&!r.fetchClientSecret?"sync"!==u.tag||s.current||!r.clientSecret&&!r.fetchClientSecret||e(u.stripe):u.stripePromise.then((function(t){t&&e(t)}))}}),[u,r,l,s]),t.useEffect((function(){return function(){l.embeddedCheckout?(c.current=null,l.embeddedCheckout.destroy()):c.current&&c.current.then((function(){c.current=null,l.embeddedCheckout&&l.embeddedCheckout.destroy()}))}}),[l.embeddedCheckout]),t.useEffect((function(){w(s)}),[s]);var f=C(n);t.useEffect((function(){null!==f&&f!==n&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the `stripe` prop after setting it.")}),[f,n]);var d=C(r);return t.useEffect((function(){null!=d&&(null!=r?(void 0===r.clientSecret&&void 0===r.fetchClientSecret&&console.warn("Invalid props passed to EmbeddedCheckoutProvider: You must provide one of either `options.fetchClientSecret` or `options.clientSecret`."),null!=d.clientSecret&&r.clientSecret!==d.clientSecret&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the client secret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead."),null!=d.fetchClientSecret&&r.fetchClientSecret!==d.fetchClientSecret&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change fetchClientSecret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead."),null!=d.onComplete&&r.onComplete!==d.onComplete&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onComplete option after setting it."),null!=d.onShippingDetailsChange&&r.onShippingDetailsChange!==d.onShippingDetailsChange&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onShippingDetailsChange option after setting it."),null!=d.onLineItemsChange&&r.onLineItemsChange!==d.onLineItemsChange&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onLineItemsChange option after setting it.")):console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot unset options after setting them."))}),[d,r]),t.createElement(L.Provider,{value:l},o)},e.EpsBankElement=Q,e.ExpressCheckoutElement=Z,e.FpxBankElement=z,e.IbanElement=G,e.IdealBankElement=J,e.LinkAuthenticationElement=ne,e.P24BankElement=K,e.PaymentElement=X,e.PaymentMethodMessagingElement=ue,e.PaymentRequestButtonElement=te,e.ShippingAddressElement=oe,e.useCustomCheckout=function(){!function(e){var n=t.useContext(N);T(n,e)}("calls useCustomCheckout()");var e=t.useContext(U);if(!e)throw new Error("Could not find CustomCheckout Context; You need to wrap the part of your app that calls useCustomCheckout() in an <CustomCheckoutProvider> provider.");return e},e.useElements=function(){return A("calls useElements()").elements},e.useStripe=function(){return B("calls useStripe()").stripe}}));
