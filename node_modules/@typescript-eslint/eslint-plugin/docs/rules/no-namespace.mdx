---
description: 'Disallow TypeScript namespaces.'
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

> 🛑 This file is source code, not the primary documentation location! 🛑
>
> See **https://typescript-eslint.io/rules/no-namespace** for documentation.

TypeScript historically allowed a form of code organization called "custom modules" (`module Example {}`), later renamed to "namespaces" (`namespace Example`).
Namespaces are an outdated way to organize TypeScript code.
ES2015 module syntax is now preferred (`import`/`export`).

> This rule does not report on the use of TypeScript module declarations to describe external APIs (`declare module 'foo' {}`).

## Examples

Examples of code with the default options:

<Tabs>
<TabItem value="❌ Incorrect">

```ts
module foo {}
namespace foo {}

declare module foo {}
declare namespace foo {}
```

</TabItem>
<TabItem value="✅ Correct">

```ts
declare module 'foo' {}

// anything inside a d.ts file
```

</TabItem>
</Tabs>

## Options

### `allowDeclarations`

Examples of code with the `{ "allowDeclarations": true }` option:

<Tabs>
<TabItem value="❌ Incorrect">

```ts option='{ "allowDeclarations": true }'
module foo {}
namespace foo {}
```

</TabItem>
<TabItem value="✅ Correct">

```ts option='{ "allowDeclarations": true }'
declare module 'foo' {}
declare module foo {}
declare namespace foo {}

declare global {
  namespace foo {}
}

declare module foo {
  namespace foo {}
}
```

</TabItem>
</Tabs>

Examples of code for the `{ "allowDeclarations": false }` option:

<Tabs>
<TabItem value="❌ Incorrect">

```ts option='{ "allowDeclarations": false }'
module foo {}
namespace foo {}
declare module foo {}
declare namespace foo {}
```

</TabItem>
<TabItem value="✅ Correct">

```ts option='{ "allowDeclarations": false }'
declare module 'foo' {}
```

</TabItem>
</Tabs>

### `allowDefinitionFiles`

Examples of code for the `{ "allowDefinitionFiles": true }` option:

<Tabs>
<TabItem value="❌ Incorrect">

```ts option='{ "allowDefinitionFiles": true }'
// if outside a d.ts file
module foo {}
namespace foo {}

// if outside a d.ts file and allowDeclarations = false
module foo {}
namespace foo {}
declare module foo {}
declare namespace foo {}
```

</TabItem>
<TabItem value="✅ Correct">

```ts option='{ "allowDefinitionFiles": true }'
declare module 'foo' {}

// anything inside a d.ts file
```

</TabItem>
</Tabs>

## When Not To Use It

If your project was architected before modern modules and namespaces, it may be difficult to migrate off of namespaces.
In that case you may not be able to use this rule for parts of your project.
You might consider using [ESLint disable comments](https://eslint.org/docs/latest/use/configure/rules#using-configuration-comments-1) for those specific situations instead of completely disabling this rule.

## Further Reading

- [Modules](https://www.typescriptlang.org/docs/handbook/modules.html)
- [Namespaces](https://www.typescriptlang.org/docs/handbook/namespaces.html)
- [Namespaces and Modules](https://www.typescriptlang.org/docs/handbook/namespaces-and-modules.html)
