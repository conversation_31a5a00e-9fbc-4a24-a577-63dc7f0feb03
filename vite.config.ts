import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],

  // Configuración del servidor de desarrollo
  server: {
    port: 3000,
    open: true
  },

  // Optimizaciones para producción
  build: {
    outDir: 'dist',
    sourcemap: true,

    // Optimizar chunks para mejor caching
    rollupOptions: {
      output: {
        manualChunks: {
          // Separar vendor chunks
          vendor: ['react', 'react-dom'],
          stripe: ['@stripe/stripe-js', '@stripe/react-stripe-js'],
          router: ['react-router-dom']
        }
      }
    },

    // Configuración de assets
    assetsDir: 'assets',

    // Límite de warnings para chunks grandes
    chunkSizeWarningLimit: 1000
  },

  // Configuración del preview
  preview: {
    port: 4173,
    open: true
  },

  // Optimizaciones de dependencias
  optimizeDeps: {
    include: ['react', 'react-dom', '@stripe/stripe-js', '@stripe/react-stripe-js']
  }
})
