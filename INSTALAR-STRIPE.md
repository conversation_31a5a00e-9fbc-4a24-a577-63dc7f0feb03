# 🚨 INSTRUCCIONES PARA ACTIVAR STRIPE

## ❌ **Error Actual:**
```
Failed to resolve import "@stripe/stripe-js"
```

## ✅ **Solución:**

### **Paso 1: Instalar Dependencias**
```bash
# Navega a tu proyecto
cd /Users/<USER>/PROJECTS/React/Anuncio-Cursos

# Instala las dependencias de Stripe
npm install @stripe/stripe-js @stripe/react-stripe-js react-router-dom

# Verifica la instalación
npm list @stripe/stripe-js
```

### **Paso 2: Activar Código de Stripe**

**En `src/components/Checkout.tsx`, descomenta estas líneas:**

#### **Líneas 6-12 (Imports):**
```typescript
// CAMBIAR ESTO:
// import { loadStripe } from '@stripe/stripe-js';

// POR ESTO:
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  CardElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';

const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY);
```

#### **Líneas 23-24 (Hooks):**
```typescript
// CAMBIAR ESTO:
// const stripe = useStripe();
// const elements = useElements();

// POR ESTO:
const stripe = useStripe();
const elements = useElements();
```

#### **Líneas 71-149 (Funciones de pago):**
```typescript
// QUITAR /* al inicio y */ al final
const handleCardPayment = async () => {
  // ... código completo
};

const handleOxxoPayment = async () => {
  // ... código completo
};
```

#### **Líneas 330-334 (Elements wrapper):**
```typescript
// CAMBIAR ESTO:
return <CheckoutForm onSuccess={onSuccess} onCancel={onCancel} />;

// POR ESTO:
return (
  <Elements stripe={stripePromise}>
    <CheckoutForm onSuccess={onSuccess} onCancel={onCancel} />
  </Elements>
);
```

### **Paso 3: Configurar Variables de Entorno**

**Edita `.env` con tus claves de Stripe:**
```env
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_TU_CLAVE_PUBLICA
STRIPE_SECRET_KEY=sk_test_TU_CLAVE_SECRETA
```

### **Paso 4: Reiniciar Servidor**
```bash
# Detén el servidor (Ctrl+C)
# Luego reinicia:
npm run dev
```

## 🎯 **Estado Actual:**

### ✅ **Lo que funciona ahora:**
- Modal de checkout se abre
- Formulario de demostración
- Validación de campos
- Simulación de pago
- Mensaje informativo

### 🚨 **Lo que necesitas hacer:**
1. **Instalar dependencias** (comando arriba)
2. **Descomentar código** de Stripe
3. **Configurar claves** en .env
4. **Reiniciar servidor**

## 📞 **¿Necesitas ayuda?**

Si tienes problemas:
1. Verifica que estás en el directorio correcto
2. Asegúrate de tener conexión a internet
3. Revisa que npm esté instalado: `npm --version`

## 🎉 **Una vez instalado:**
- ✅ Pagos reales con tarjeta
- ✅ Códigos OXXO funcionales
- ✅ Validación de Stripe
- ✅ Manejo de errores profesional
