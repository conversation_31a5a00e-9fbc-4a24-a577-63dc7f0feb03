# 🚀 Guía Completa de Deploy - Netlify

## 📋 **<PERSON><PERSON><PERSON> Completo Optimizado**

### **✅ Paso 1: Configurar Stripe y Tests**
### **💰 Paso 2: Cambiar a Dinero Real**
### **🏗️ Paso 3: Build Optimizado**
### **🌐 Paso 4: Deploy en Netlify**

---

## 🔧 **Paso 1: Configurar Stripe y Tests**

### **1.1 Instalar Dependencias:**
```bash
npm install @stripe/stripe-js @stripe/react-stripe-js react-router-dom --legacy-peer-deps
```

### **1.2 Configurar .env para Testing:**
```env
# Claves de TEST de Stripe
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_TU_CLAVE_TEST
STRIPE_SECRET_KEY=sk_test_TU_CLAVE_TEST
```

### **1.3 Activar Código de Stripe:**
- Descomentar imports en `src/components/Checkout.tsx`
- Activar funciones de pago
- Habilitar Elements wrapper

### **1.4 Probar con Tarjetas de Test:**
- **Éxito**: `4242 4242 4242 4242`
- **Fallo**: `4000 0000 0000 0002`
- **OXXO**: Verificar generación de código

---

## 💰 **Paso 2: Cambiar a Dinero Real**

### **2.1 Activar Cuenta de Stripe:**
1. Completar verificación de identidad
2. Agregar información bancaria
3. Activar cuenta para pagos reales

### **2.2 Obtener Claves de Producción:**
```bash
# En dashboard.stripe.com/apikeys
# Cambiar de "Test data" a "Live data"
pk_live_... # Clave pública LIVE
sk_live_... # Clave secreta LIVE
```

### **2.3 Configurar Variables de Producción:**
```env
# .env.production
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_TU_CLAVE_LIVE
STRIPE_SECRET_KEY=sk_live_TU_CLAVE_LIVE
```

---

## 🏗️ **Paso 3: Build Optimizado**

### **3.1 Verificar Configuración:**
```bash
# Verificar que todo esté listo
npm run lint
npm audit
```

### **3.2 Build para Producción:**
```bash
# Build optimizado para Netlify
npm run build:netlify
```

### **3.3 Verificar Build:**
```bash
# Preview local del build
npm run preview
```

### **3.4 Analizar Bundle (Opcional):**
```bash
# Ver tamaño de archivos
npm run analyze
```

---

## 🌐 **Paso 4: Deploy en Netlify**

### **4.1 Preparar Repositorio:**
```bash
# Commit todos los cambios
git add .
git commit -m "🚀 Ready for production deploy"
git push origin main
```

### **4.2 Conectar con Netlify:**
1. Ve a **https://netlify.com**
2. **"New site from Git"**
3. Conecta tu repositorio de GitHub
4. Configuración automática detectada

### **4.3 Configurar Variables de Entorno:**
```bash
# En Netlify Dashboard > Site settings > Environment variables
VITE_STRIPE_PUBLISHABLE_KEY = pk_live_TU_CLAVE_LIVE
STRIPE_SECRET_KEY = sk_live_TU_CLAVE_SECRETA
VITE_APP_URL = https://cursos.willdelavega.com
VITE_SUCCESS_URL = https://cursos.willdelavega.com/success
VITE_CANCEL_URL = https://cursos.willdelavega.com/cancel
VITE_PRODUCT_NAME = Curso de Inteligencia Artificial 2024
VITE_PRODUCT_PRICE = 50000
VITE_CURRENCY = mxn
NODE_ENV = production
```

### **4.4 Configurar Dominio Personalizado:**
1. **Site settings** > **Domain management**
2. **Add custom domain**: `cursos.willdelavega.com`
3. Configurar DNS en tu proveedor:
   ```
   CNAME cursos -> tu-sitio.netlify.app
   ```
4. **Enable HTTPS** (automático)

### **4.5 Configurar Redirects:**
- ✅ Ya configurado en `netlify.toml`
- ✅ Archivo `_redirects` incluido
- ✅ SPA routing funcionará correctamente

---

## 🔍 **Optimizaciones Implementadas**

### **✅ Performance:**
- **Code splitting** por vendor/stripe/router
- **Lazy loading** de componentes
- **Asset optimization** automática
- **Gzip compression** habilitada

### **✅ SEO:**
- **Meta tags** optimizados
- **Sitemap.xml** incluido
- **Robots.txt** configurado
- **Open Graph** completo

### **✅ Seguridad:**
- **Headers de seguridad** configurados
- **HTTPS** forzado
- **Variables de entorno** seguras
- **No exposición** de claves secretas

### **✅ Caching:**
- **Assets estáticos**: 1 año
- **HTML**: Sin cache (siempre fresco)
- **API responses**: Configurado

---

## 📊 **Checklist de Deploy**

### **Pre-Deploy:**
- [ ] Dependencias instaladas
- [ ] Stripe configurado y probado
- [ ] Variables de entorno configuradas
- [ ] Build exitoso localmente
- [ ] Preview funcionando

### **Deploy:**
- [ ] Repositorio actualizado
- [ ] Netlify conectado
- [ ] Variables de entorno en Netlify
- [ ] Dominio personalizado configurado
- [ ] HTTPS habilitado

### **Post-Deploy:**
- [ ] Sitio accesible
- [ ] Pagos funcionando
- [ ] Redirects funcionando
- [ ] SEO verificado
- [ ] Performance optimizada

---

## 🚨 **Troubleshooting**

### **Error: "Build failed"**
```bash
# Verificar dependencias
npm install
npm run build
```

### **Error: "Environment variables not found"**
- Verificar variables en Netlify Dashboard
- Usar prefijo VITE_ para variables del frontend

### **Error: "404 on refresh"**
- Verificar archivo `_redirects`
- Confirmar configuración SPA en `netlify.toml`

### **Error: "Stripe not loading"**
- Verificar clave pública en variables de entorno
- Confirmar que es clave LIVE para producción

---

## 🎉 **¡Deploy Exitoso!**

Tu sitio estará disponible en:
- **URL temporal**: `https://tu-sitio.netlify.app`
- **Dominio personalizado**: `https://cursos.willdelavega.com`

### **Funcionalidades Activas:**
- ✅ **Pagos reales** con Stripe
- ✅ **OXXO** para México
- ✅ **SEO optimizado**
- ✅ **Performance máximo**
- ✅ **Seguridad completa**
- ✅ **Analytics ready**

## 📈 **Próximos Pasos:**
1. **Configurar Google Analytics**
2. **Monitorear conversiones**
3. **Optimizar según métricas**
4. **Escalar según demanda**
