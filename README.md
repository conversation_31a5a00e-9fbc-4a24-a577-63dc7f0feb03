# Anuncio Cursos - Landing Page

Landing page para el curso de Inteligencia Artificial impartido por Will de la Vega.

## 🚀 Tecnologías

- **React 18** - Biblioteca de JavaScript para construir interfaces de usuario
- **TypeScript** - Superset de JavaScript con tipado estático
- **Vite** - Herramienta de build rápida y moderna
- **React Icons** - Biblioteca de iconos para React
- **Canvas API** - Animaciones de partículas custom para efectos visuales
- **CSS3** - Estilos modernos con variables CSS y Flexbox/Grid

## 📦 Instalación

1. Clona el repositorio
2. Instala las dependencias:
   ```bash
   npm install
   ```

## 🛠️ Scripts Disponibles

- `npm run dev` - Inicia el servidor de desarrollo
- `npm run build` - Construye la aplicación para producción
- `npm run preview` - Previsualiza la build de producción
- `npm run lint` - Ejecuta el linter para revisar el código

## 🏗️ Estructura del Proyecto

```
anuncio-cursos/
├── public/
│   └── vite.svg
├── src/
│   ├── app/
│   │   ├── App.tsx
│   │   └── App.css
│   ├── assets/
│   │   └── images/
│   ├── styles/
│   │   └── index.css
│   └── main.tsx
├── index.html
├── package.json
├── tsconfig.json
├── tsconfig.node.json
├── vite.config.ts
└── eslint.config.js
```

## 🎨 Características

- **Diseño Responsivo** - Adaptado para dispositivos móviles y desktop
- **Optimizado para SEO** - Meta tags y estructura semántica
- **Accesibilidad** - Cumple con estándares de accesibilidad web
- **Performance** - Optimizado para carga rápida
- **TypeScript** - Tipado estático para mejor desarrollo
- **Animaciones de Partículas** - Efectos visuales de red neuronal en hero section
- **Esquema de Colores Científico** - Basado en estudios de neuromarketing
- **Optimizado para Conversión** - Colores y diseño basados en evidencia científica

## 📱 Funcionalidades

- Hero section con call-to-action
- Sección de temas del curso
- Información del instructor
- Testimonios de alumnos
- Preguntas frecuentes
- Botón flotante de WhatsApp
- Formulario de contacto

## 🔧 Configuración

El proyecto está configurado con:
- **ESLint** para mantener la calidad del código
- **TypeScript** con configuración estricta
- **Vite** para desarrollo y build optimizado

## 📄 Licencia

MIT

## 👨‍💻 Autor

Will de la Vega - Desarrollador FullStack con 35 años de experiencia
