<svg width="1280" height="640" viewBox="0 0 1280 640" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="1280" height="640" fill="#2563eb"/>
  <g transform="translate(640, 320)">
    <!-- Brain outline -->
    <path d="M-120 -80 C-140 -100, -140 -120, -100 -120 C-60 -130, -20 -130, 20 -120 C60 -130, 100 -120, 120 -100 C140 -80, 140 -40, 120 -20 C100 0, 80 20, 60 40 C40 60, 20 80, 0 80 C-20 80, -40 60, -60 40 C-80 20, -100 0, -120 -20 C-140 -40, -140 -80, -120 -80 Z" 
          fill="none" stroke="#ffffff" stroke-width="3"/>
    
    <!-- Neural network lines -->
    <g stroke="#10b981" stroke-width="2" opacity="0.8">
      <line x1="-80" y1="-40" x2="-40" y2="-20"/>
      <line x1="-40" y1="-20" x2="0" y2="-40"/>
      <line x1="0" y1="-40" x2="40" y2="-20"/>
      <line x1="40" y1="-20" x2="80" y2="-40"/>
      
      <line x1="-60" y1="0" x2="-20" y2="20"/>
      <line x1="-20" y1="20" x2="20" y2="0"/>
      <line x1="20" y1="0" x2="60" y2="20"/>
      
      <line x1="-40" y1="40" x2="0" y2="60"/>
      <line x1="0" y1="60" x2="40" y2="40"/>
    </g>
    
    <!-- Neural nodes -->
    <g fill="#f59e0b">
      <circle cx="-80" cy="-40" r="4"/>
      <circle cx="-40" cy="-20" r="4"/>
      <circle cx="0" cy="-40" r="4"/>
      <circle cx="40" cy="-20" r="4"/>
      <circle cx="80" cy="-40" r="4"/>
      
      <circle cx="-60" cy="0" r="4"/>
      <circle cx="-20" cy="20" r="4"/>
      <circle cx="20" cy="0" r="4"/>
      <circle cx="60" cy="20" r="4"/>
      
      <circle cx="-40" cy="40" r="4"/>
      <circle cx="0" cy="60" r="4"/>
      <circle cx="40" cy="40" r="4"/>
    </g>
    
    <!-- AI Text -->
    <text x="0" y="120" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="24" font-weight="bold">
      INTELIGENCIA ARTIFICIAL
    </text>
  </g>
  
  <!-- Decorative elements -->
  <g opacity="0.3">
    <circle cx="100" cy="100" r="2" fill="#ffffff"/>
    <circle cx="1180" cy="540" r="2" fill="#ffffff"/>
    <circle cx="200" cy="500" r="1.5" fill="#10b981"/>
    <circle cx="1080" cy="140" r="1.5" fill="#f59e0b"/>
  </g>
</svg>
