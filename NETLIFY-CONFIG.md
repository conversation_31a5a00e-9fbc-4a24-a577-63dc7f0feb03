# 🚀 Configuración de Netlify - Datos Bancarios

## 🏦 **Variables de Entorno para Netlify**

### **En Netlify Dashboard > Site settings > Environment variables, agrega:**

```env
# 🏦 INFORMACIÓN BANCARIA
VITE_BANK_NAME = BBVA México
VITE_BANK_ACCOUNT = **********
VITE_BANK_CLABE = 0129100**********3
VITE_BANK_HOLDER = <PERSON>

# 🌐 URLs DE PRODUCCIÓN
VITE_APP_URL = https://cursos.willdelavega.com
VITE_SUCCESS_URL = https://cursos.willdelavega.com/success
VITE_CANCEL_URL = https://cursos.willdelavega.com/cancel

# 💰 INFORMACIÓN DEL PRODUCTO
VITE_PRODUCT_NAME = Curso de Inteligencia Artificial 2024
VITE_PRODUCT_PRICE = 50000
VITE_CURRENCY = mxn

# 🔧 CONFIGURACIÓN DE PRODUCCIÓN
NODE_ENV = production
VITE_APP_ENV = production
```

## 📋 **Datos Bancarios Configurados**

### ✅ **Información Completa:**
- **Banco**: BBVA México
- **Cuenta**: **********
- **CLABE**: 0129100**********3
- **Titular**: William Baez Vega
- **Concepto**: Curso de IA - [Nombre del estudiante]

### 📱 **Flujo de Pago:**
1. **Cliente** llena formulario
2. **Ve datos bancarios** con botones de copiar
3. **Realiza transferencia** SPEI o depósito
4. **Envía comprobante** por WhatsApp/Email
5. **Tú confirmas** manualmente
6. **Cliente recibe** acceso al curso

## 🎯 **Mensajes Pre-configurados**

### **WhatsApp:**
```
Hola Will, quiero inscribirme al Curso de Inteligencia Artificial 2024.

📝 Mis datos:
• Nombre: [Nombre del cliente]
• Email: [Email del cliente]
• Teléfono: [Teléfono del cliente]

💰 Realizaré el depósito de $500 MXN y te enviaré el comprobante.

¡Gracias!
```

### **Email:**
```
Asunto: Inscripción Curso IA 2024 - Pago por Transferencia

Hola Will,

Quiero inscribirme al Curso de Inteligencia Artificial 2024.

Mis datos:
• Nombre: [Nombre del cliente]
• Email: [Email del cliente]
• Teléfono: [Teléfono del cliente]

Realizaré el depósito bancario de $500 MXN y te enviaré el comprobante de pago.

Saludos,
[Nombre del cliente]
```

## 🚀 **Deploy Checklist**

### **Pre-Deploy:**
- [x] Datos bancarios configurados
- [x] Variables de entorno actualizadas
- [x] Interfaz bancaria funcionando
- [x] Stripe deshabilitado (preservado)

### **Deploy:**
- [ ] Push a repositorio
- [ ] Conectar con Netlify
- [ ] Configurar variables de entorno
- [ ] Configurar dominio personalizado
- [ ] Probar flujo completo

### **Post-Deploy:**
- [ ] Verificar datos bancarios en sitio
- [ ] Probar botones de copiar
- [ ] Verificar WhatsApp/Email
- [ ] Confirmar responsive design

## 📞 **Contacto Configurado**

### **WhatsApp:**
- **Número**: +52 ************
- **Mensaje**: Pre-formateado con datos del cliente

### **Email:**
- **Dirección**: <EMAIL>
- **Asunto**: Pre-configurado
- **Cuerpo**: Con toda la información

## 🔄 **Para Reactivar Stripe (Futuro)**

### **Cuando resuelvas regulaciones:**

1. **Descomentar en App.tsx:**
```typescript
import Checkout from '../components/Checkout'; // ✅ REACTIVAR
// import BankTransferCheckout from '../components/BankTransferCheckout';
```

2. **Cambiar componente:**
```typescript
<Checkout onSuccess={handlePaymentSuccess} onCancel={handleCheckoutCancel} />
```

3. **Activar variables Stripe:**
```env
VITE_STRIPE_PUBLISHABLE_KEY = pk_live_TU_CLAVE
STRIPE_SECRET_KEY = sk_live_TU_CLAVE
```

## ✅ **¡Listo para Lanzar!**

Tu sistema de pago bancario está completamente configurado:
- ✅ **Datos reales** de BBVA
- ✅ **Interfaz profesional**
- ✅ **WhatsApp/Email** integrados
- ✅ **Responsive design**
- ✅ **Fácil reactivación** de Stripe

¡Puedes hacer deploy inmediatamente!
