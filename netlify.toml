# Configuración optimizada para Netlify
[build]
  # Comando de build
  command = "npm run build"
  
  # Directorio de salida
  publish = "dist"
  
  # Variables de entorno para build
  environment = { NODE_ENV = "production" }

# Configuración de redirects para SPA
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Headers de seguridad y performance
[[headers]]
  for = "/*"
  [headers.values]
    # Seguridad
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    X-XSS-Protection = "1; mode=block"
    Referrer-Policy = "strict-origin-when-cross-origin"
    
    # Performance
    Cache-Control = "public, max-age=31536000, immutable"

# Headers específicos para archivos estáticos
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Headers para HTML (sin cache)
[[headers]]
  for = "/*.html"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"

# Headers para archivos de configuración
[[headers]]
  for = "/manifest.json"
  [headers.values]
    Cache-Control = "public, max-age=86400"

[[headers]]
  for = "/robots.txt"
  [headers.values]
    Cache-Control = "public, max-age=86400"

[[headers]]
  for = "/sitemap.xml"
  [headers.values]
    Cache-Control = "public, max-age=86400"

# Configuración de formularios (para contacto futuro)
[build.processing]
  skip_processing = false

[build.processing.css]
  bundle = true
  minify = true

[build.processing.js]
  bundle = true
  minify = true

[build.processing.html]
  pretty_urls = true

# Variables de entorno para producción
[context.production.environment]
  NODE_ENV = "production"
  VITE_APP_ENV = "production"

# Variables para preview/staging
[context.deploy-preview.environment]
  NODE_ENV = "staging"
  VITE_APP_ENV = "staging"
