# Variables de entorno para PRODUCCIÓN en Netlify
# ⚠️ IMPORTANTE: Configura estas variables en el dashboard de Netlify

# 🔑 STRIPE PRODUCCIÓN (configurar en Netlify Dashboard)
# VITE_STRIPE_PUBLISHABLE_KEY=pk_live_TU_CLAVE_PUBLICA_REAL
# STRIPE_SECRET_KEY=sk_live_TU_CLAVE_SECRETA_REAL

# 🌐 URLs DE PRODUCCIÓN
VITE_APP_URL=https://cursos.willdelavega.com
VITE_SUCCESS_URL=https://cursos.willdelavega.com/success
VITE_CANCEL_URL=https://cursos.willdelavega.com/cancel

# 💰 INFORMACIÓN DEL PRODUCTO
VITE_PRODUCT_NAME=Curso de Inteligencia Artificial 2024
VITE_PRODUCT_PRICE=50000
VITE_CURRENCY=mxn

# 🔧 CONFIGURACIÓN DE PRODUCCIÓN
NODE_ENV=production
VITE_APP_ENV=production

# 📧 CONFIGURACIÓN DE EMAIL (opcional)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=tu-app-password

# 📊 ANALYTICS (configurar si usas)
# VITE_GA_TRACKING_ID=G-XXXXXXXXXX
# VITE_HOTJAR_ID=tu-hotjar-id

# ========================================
# INSTRUCCIONES PARA NETLIFY:
# 
# 1. Ve a tu dashboard de Netlify
# 2. Selecciona tu sitio
# 3. Ve a Site settings > Environment variables
# 4. Agrega cada variable (sin el prefijo #)
# 5. Usa las claves LIVE de Stripe para producción
# ========================================
